[server]
host = "0.0.0.0"
port = 3000
workers = 0
enable_docs = true
request_timeout_seconds = 40
allowed_origins = ["https://staging.vulnera.dev"]

[server.security]
enforce_https = true
enable_security_headers = true
sanitize_errors = true
hsts_max_age = 31536000
hsts_include_subdomains = true

[cache]
ttl_hours = 12

[apis.osv]
base_url = "https://api.osv.dev/v1"
timeout_seconds = 20

[apis.nvd]
base_url = "https://services.nvd.nist.gov/rest/json"
api_key = "${NVD_API_KEY}"
timeout_seconds = 25
rate_limit_per_30s = 50

[apis.ghsa]
graphql_url = "https://api.github.com/graphql"
token = "${GH_TOKEN}"
timeout_seconds = 25

[logging]
level = "info"
format = "json"
