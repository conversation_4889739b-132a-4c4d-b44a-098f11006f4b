# Redis Sentinel Configuration for Vulnera
# This configuration provides high availability for Redis cache

# Sentinel port
port 26379

# Sentinel working directory
dir /tmp

# Monitor the master Redis instance
# sentinel monitor <master-name> <ip> <port> <quorum>
sentinel monitor vulnera-master redis 6379 1

# Authentication (if <PERSON><PERSON> requires auth)
# sentinel auth-pass vulnera-master your_redis_password

# Master down after 5 seconds of no response
sentinel down-after-milliseconds vulnera-master 5000

# Failover timeout
sentinel failover-timeout vulnera-master 10000

# Number of slaves that can be reconfigured simultaneously
sentinel parallel-syncs vulnera-master 1

# Logging
logfile /var/log/sentinel.log
loglevel notice

# Deny dangerous commands
sentinel deny-scripts-reconfig yes

# Notification scripts (optional)
# sentinel notification-script vulnera-master /path/to/notify.sh
# sentinel client-reconfig-script vulnera-master /path/to/reconfig.sh
