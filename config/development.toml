[server]
host = "0.0.0.0"
port = 3000
workers = 0
enable_docs = true
request_timeout_seconds = 60
allowed_origins = ["*"]

[server.security]
enforce_https = false           # Don't enforce HTTPS in development
enable_security_headers = true  # But enable security headers for testing
sanitize_errors = false         # Show full errors in development
hsts_max_age = 31536000
hsts_include_subdomains = false # Don't include subdomains in dev

[cache]
ttl_hours = 1

[apis.osv]
base_url = "https://api.osv.dev/v1"
timeout_seconds = 30

[apis.nvd]
base_url = "https://services.nvd.nist.gov/rest/json"
api_key = ""
timeout_seconds = 30
rate_limit_per_30s = 5

[apis.ghsa]
graphql_url = "https://api.github.com/graphql"
token = ""
timeout_seconds = 30

[logging]
level = "debug"
format = "pretty"
