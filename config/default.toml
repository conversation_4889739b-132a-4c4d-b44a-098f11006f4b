# Default configuration for Vulnera Rust

[server]
host = "0.0.0.0"
port = 3000

[cache]
directory = ".vulnera_cache"
ttl_hours = 24
strategy = "Hybrid"  # Options: "File", "Redis", "Hybrid"

[redis]
url = "redis://localhost:6379"
pool_size = 10
connection_timeout_ms = 5000
command_timeout_ms = 3000
retry_attempts = 3
retry_delay_ms = 100
key_prefix = "vulnera:"

[redis.cluster]
enabled = false
nodes = []
read_from_replicas = true

[redis.sentinel]
enabled = false
service_name = "vulnera-master"
nodes = ["localhost:26379"]

[redis.compression]
enabled = true
algorithm = "Lz4"  # Options: "Gzip", "Lz4", "None"
min_size_bytes = 1024

[apis.osv]
base_url = "https://api.osv.dev/v1"
timeout_seconds = 30

[apis.nvd]
base_url = "https://services.nvd.nist.gov/rest/json"
timeout_seconds = 30
rate_limit_per_30s = 5  # Without API key, 50 with API key

[apis.ghsa]
graphql_url = "https://api.github.com/graphql"
timeout_seconds = 30

[logging]
level = "info"
format = "json"