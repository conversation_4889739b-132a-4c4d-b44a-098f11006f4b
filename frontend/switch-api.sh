#!/bin/bash
# Enhanced script to switch between different API configurations
# Now includes timeout, debug settings, and environment detection

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to create environment file
create_env_file() {
    local api_url="$1"
    local app_name="$2" 
    local timeout="$3"
    local debug="$4"
    local environment="$5"
    
    cat > .env << EOF
# Generated by switch-api.sh on $(date)
VITE_API_BASE_URL=${api_url}
VITE_API_VERSION=v1
VITE_APP_NAME=${app_name}
VITE_APP_VERSION=1.0.0
VITE_ENABLE_DEBUG=${debug}
VITE_API_TIMEOUT=${timeout}
VITE_ENVIRONMENT=${environment}
EOF
}

# Function to test API connectivity
test_api_connection() {
    local url="$1"
    echo -e "${BLUE}🔍 Testing API connectivity...${NC}"
    
    if command -v curl > /dev/null 2>&1; then
        if curl -s --max-time 5 "${url}/health" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ API is reachable${NC}"
        else
            echo -e "${YELLOW}⚠️  API might not be reachable (this is normal if the server is not running)${NC}"
        fi
    else
        echo -e "${YELLOW}ℹ️  curl not available, skipping connectivity test${NC}"
    fi
}

case "$1" in
    "local")
        create_env_file "http://localhost:3000" "Vulnera Dev" "30000" "true" "development"
        echo -e "${GREEN}✅ Switched to local API (http://localhost:3000)${NC}"
        test_api_connection "http://localhost:3000"
        ;;
    "remote")
        create_env_file "http://************:3000" "Vulnera Remote" "45000" "true" "development"
        echo -e "${GREEN}✅ Switched to remote API (http://************:3000)${NC}"
        test_api_connection "http://************:3000"
        ;;
    "azure")
        create_env_file "https://vulnera-back.politeisland-d68133bc.switzerlandnorth.azurecontainerapps.io" "Vulnera Azure" "60000" "true" "staging"
        echo -e "${GREEN}✅ Switched to Azure API${NC}"
        echo -e "${BLUE}URL: https://vulnera-back.politeisland-d68133bc.switzerlandnorth.azurecontainerapps.io${NC}"
        test_api_connection "https://vulnera-back.politeisland-d68133bc.switzerlandnorth.azurecontainerapps.io"
        ;;
    "prod"|"production")
        create_env_file "https://api.vulnera.dev" "Vulnera" "60000" "false" "production"
        echo -e "${GREEN}✅ Switched to production API (https://api.vulnera.dev)${NC}"
        test_api_connection "https://api.vulnera.dev"
        ;;
    "network")
        echo -en "${BLUE}Enter network IP (e.g., *************): ${NC}"
        read network_ip
        if [[ -z "$network_ip" ]]; then
            echo -e "${RED}❌ No IP provided. Aborting.${NC}"
            exit 1
        fi
        create_env_file "http://${network_ip}:3000" "Vulnera Network" "30000" "true" "development"
        echo -e "${GREEN}✅ Switched to network API (http://${network_ip}:3000)${NC}"
        test_api_connection "http://${network_ip}:3000"
        ;;
    "custom")
        echo -en "${BLUE}Enter custom API URL: ${NC}"
        read custom_url
        if [[ -z "$custom_url" ]]; then
            echo -e "${RED}❌ No URL provided. Aborting.${NC}"
            exit 1
        fi
        
        # Validate URL format
        if [[ ! "$custom_url" =~ ^https?:// ]]; then
            echo -e "${YELLOW}⚠️  Adding http:// prefix to URL${NC}"
            custom_url="http://${custom_url}"
        fi
        
        create_env_file "$custom_url" "Vulnera Custom" "45000" "true" "development"
        echo -e "${GREEN}✅ Switched to custom API (${custom_url})${NC}"
        test_api_connection "$custom_url"
        ;;
    "dev"|"development")
        if [[ -f ".env.development" ]]; then
            cp .env.development .env
            echo -e "${GREEN}✅ Switched to development environment (using .env.development)${NC}"
        else
            create_env_file "http://localhost:3000" "Vulnera Dev" "30000" "true" "development"
            echo -e "${GREEN}✅ Switched to development environment${NC}"
        fi
        test_api_connection "http://localhost:3000"
        ;;
    "staging")
        if [[ -f ".env.staging" ]]; then
            cp .env.staging .env
            echo -e "${GREEN}✅ Switched to staging environment (using .env.staging)${NC}"
        else
            create_env_file "https://vulnera-back.politeisland-d68133bc.switzerlandnorth.azurecontainerapps.io" "Vulnera Staging" "60000" "true" "staging"
            echo -e "${GREEN}✅ Switched to staging environment${NC}"
        fi
        ;;
    "status"|"show")
        echo -e "${BLUE}📋 Current Configuration:${NC}"
        if [[ -f ".env" ]]; then
            echo ""
            while IFS= read -r line; do
                if [[ "$line" =~ ^VITE_ ]]; then
                    echo -e "${GREEN}  $line${NC}"
                elif [[ "$line" =~ ^# ]]; then
                    echo -e "${YELLOW}  $line${NC}"
                fi
            done < .env
            echo ""
            
            # Extract API URL for testing
            if command -v grep > /dev/null 2>&1; then
                api_url=$(grep "VITE_API_BASE_URL" .env 2>/dev/null | cut -d'=' -f2)
                if [[ -n "$api_url" ]]; then
                    test_api_connection "$api_url"
                fi
            fi
        else
            echo -e "${YELLOW}  No .env file found${NC}"
        fi
        ;;
    *)
        echo -e "${BLUE}Usage: $0 {local|remote|azure|production|network|custom|dev|staging|status}${NC}"
        echo ""
        echo -e "${YELLOW}Available options:${NC}"
        echo -e "  ${GREEN}local${NC}      - http://localhost:3000 (development)"
        echo -e "  ${GREEN}remote${NC}     - http://************:3000 (remote server)"
        echo -e "  ${GREEN}azure${NC}      - Azure Container Apps (staging)"
        echo -e "  ${GREEN}production${NC} - https://api.vulnera.dev (production)"
        echo -e "  ${GREEN}dev${NC}        - Use .env.development file"
        echo -e "  ${GREEN}staging${NC}    - Use .env.staging file"
        echo -e "  ${GREEN}network${NC}    - Custom network IP"
        echo -e "  ${GREEN}custom${NC}     - Custom URL"
        echo -e "  ${GREEN}status${NC}     - Show current configuration"
        echo ""
        echo -e "${BLUE}Environment files:${NC}"
        echo -e "  ${YELLOW}.env.development${NC} - Development settings"
        echo -e "  ${YELLOW}.env.staging${NC}     - Staging settings"
        echo -e "  ${YELLOW}.env.production${NC}  - Production settings"
        echo -e "  ${YELLOW}.env.example${NC}     - Example configuration"
        echo ""
        echo -e "${BLUE}Current configuration:${NC}"
        if [[ -f ".env" ]]; then
            echo -e "${GREEN}$(cat .env 2>/dev/null | grep -E '^VITE_')${NC}"
        else
            echo -e "${YELLOW}No .env file found${NC}"
        fi
        exit 1
        ;;
esac

echo ""
echo -e "${BLUE}📝 Configuration saved to .env${NC}"
echo -e "${YELLOW}ℹ️  Don't forget to restart your development server if it's running${NC}"

# Show quick restart command
if command -v npm > /dev/null 2>&1; then
    echo -e "${BLUE}   Run: npm run dev${NC}"
elif command -v yarn > /dev/null 2>&1; then
    echo -e "${BLUE}   Run: yarn dev${NC}"
else
    echo -e "${BLUE}   Run your development server${NC}"
fi
