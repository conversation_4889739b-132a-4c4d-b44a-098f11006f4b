# Example environment configuration for Vulnera Frontend
# Copy this file to .env and customize for your setup

# ===============================================
# API Configuration
# ===============================================

# API Base URL - set this to your backend server
VITE_API_BASE_URL=http://localhost:3000

# API Version (usually v1)
VITE_API_VERSION=v1

# API Request timeout in milliseconds
VITE_API_TIMEOUT=30000

# ===============================================
# Application Configuration  
# ===============================================

# Application name (shown in UI)
VITE_APP_NAME=Vulnera

# Application version
VITE_APP_VERSION=1.0.0

# Environment name
VITE_ENVIRONMENT=development

# Enable debug logging (true/false)
VITE_ENABLE_DEBUG=true

# ===============================================
# Common Examples:
# ===============================================

# Local development (default)
# VITE_API_BASE_URL=http://localhost:3000
# VITE_ENABLE_DEBUG=true

# Network/LAN development  
# VITE_API_BASE_URL=http://*************:3000
# VITE_APP_NAME=Vulnera Network

# Remote server
# VITE_API_BASE_URL=http://************:3000
# VITE_APP_NAME=Vulnera Remote

# Azure staging
# VITE_API_BASE_URL=https://vulnera-back.politeisland-d68133bc.switzerlandnorth.azurecontainerapps.io
# VITE_APP_NAME=Vulnera Azure
# VITE_API_TIMEOUT=45000

# Production
# VITE_API_BASE_URL=https://api.vulnera.dev
# VITE_APP_NAME=Vulnera
# VITE_ENABLE_DEBUG=false
# VITE_API_TIMEOUT=60000

# ===============================================
# Runtime Configuration (Alternative)
# ===============================================
# You can also set these via window object in index.html:
# 
# <script>
#   window.VULNERA_API_BASE_URL = 'https://api.example.com';
#   window.VULNERA_API_VERSION = 'v1';
#   window.VULNERA_APP_NAME = 'Custom Vulnera';
# </script>
# 
# This allows for runtime configuration without rebuilding.
