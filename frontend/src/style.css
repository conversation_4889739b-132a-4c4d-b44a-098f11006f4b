@import "tailwindcss";
@plugin "daisyui";

/* Reset default body styles for full-height layout */
body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Custom breakpoint utilities */
.xs\:inline {
  @media (min-width: 475px) {
    display: inline;
  }
}

.xs\:hidden {
  @media (min-width: 475px) {
    display: none;
  }
}

/* Custom DaisyUI theme animations */
.swap-off, .swap-on {
  transition: all 0.3s ease;
}

/* Drag and drop animations */
@keyframes bounce-gentle {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.bounce-gentle {
  animation: bounce-gentle 2s infinite;
}

/* Language cards hover effect with responsive scaling */
.language-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

@media (max-width: 640px) {
  .language-card:hover {
    transform: translateY(-2px);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

@media (min-width: 640px) {
  ::-webkit-scrollbar {
    width: 8px;
  }
}

::-webkit-scrollbar-track {
  background: hsl(var(--b2));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--p));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--pf));
}

/* File drop zone animation with responsive scaling */
#drop-zone {
  transition: all 0.3s ease;
}

#drop-zone:hover {
  transform: scale(1.01);
}

@media (max-width: 640px) {
  #drop-zone:hover {
    transform: scale(1.005);
  }
}

/* Loading animation */
@keyframes pulse-soft {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.pulse-soft {
  animation: pulse-soft 2s infinite;
}

/* Smooth transitions for theme switching */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Mobile-specific improvements */
@media (max-width: 640px) {
  /* Reduce motion for better mobile experience */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Better touch targets */
  .btn {
    min-height: 3rem;
  }

  /* Improved text legibility on small screens */
  body {
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }
}

/* Focus styles for better accessibility */
@media (prefers-reduced-motion: no-preference) {
  :focus {
    outline: 2px solid hsl(var(--p));
    outline-offset: 2px;
  }
}

/* GitHub scan button animation */
#github-scan-btn {
  transition: transform 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

#github-scan-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

#github-scan-btn:active {
  transform: scale(0.98);
}

/* Notyf custom styling to match DaisyUI theme */
.notyf__toast {
  border-radius: 0.5rem !important;
  font-family: inherit !important;
}

.notyf__wrapper {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

/* Coming Soon section styling */
.card.bg-gradient-to-r {
  background: linear-gradient(to right,
    hsl(var(--wa) / 0.15),
    hsl(var(--su) / 0.1)
  );
  backdrop-filter: blur(10px);
  border: 1px solid hsl(var(--wa) / 0.3);
}

[data-theme="light"] .card.bg-gradient-to-r {
  background: linear-gradient(to right,
    #fef3c7,
    #fef7cd
  );
  border: 1px solid #f59e0b;
  color: #92400e;
}

[data-theme="dark"] .card.bg-gradient-to-r {
  background: linear-gradient(to right, hsl(var(--wa) / 0.2), hsl(var(--er) / 0.1));
  border: 1px solid hsl(var(--wa) / 0.4);
}

/* Enhanced preview button animation */
#github-scan-preview {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

#github-scan-preview:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

#github-scan-preview:hover:before {
  left: 100%;
}

#github-scan-preview:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}
