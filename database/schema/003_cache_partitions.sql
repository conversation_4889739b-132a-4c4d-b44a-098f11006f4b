-- PostgreSQL Cache Table Partitioning Strategy
-- Version: 1.0
-- Description: Partitioning for large-scale cache operations

SET search_path TO cache, public;

-- ============================================================================
-- PARTITION CACHE_AUDIT_LOG BY TIME (Most Important for Performance)
-- ============================================================================

-- Drop existing table and recreate as partitioned
-- Note: This should be done during initial setup, not on existing data
-- For existing installations, use pg_partman or manual migration

-- Create partitioned audit log table
DROP TABLE IF EXISTS cache_audit_log CASCADE;

CREATE TABLE cache_audit_log (
    id UUID DEFAULT uuid_generate_v4(),
    cache_key VARCHAR(512),
    operation VARCHAR(20) NOT NULL CHECK (operation IN ('get', 'set', 'delete', 'expire', 'clear')),
    user_id VARCHAR(100),
    session_id VARCHAR(100),
    operation_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    result VARCHAR(20) NOT NULL CHECK (result IN ('hit', 'miss', 'error', 'success')),
    response_time_ms BIGINT,
    metadata JSONB DEFAULT '{}'::jsonb,
    PRIMARY KEY (id, operation_at)
) PARTITION BY RANGE (operation_at);

-- Create monthly partitions for audit log (last 6 months + next 6 months)
CREATE TABLE cache_audit_log_2024_01 PARTITION OF cache_audit_log
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

CREATE TABLE cache_audit_log_2024_02 PARTITION OF cache_audit_log
    FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');

CREATE TABLE cache_audit_log_2024_03 PARTITION OF cache_audit_log
    FOR VALUES FROM ('2024-03-01') TO ('2024-04-01');

CREATE TABLE cache_audit_log_2024_04 PARTITION OF cache_audit_log
    FOR VALUES FROM ('2024-04-01') TO ('2024-05-01');

CREATE TABLE cache_audit_log_2024_05 PARTITION OF cache_audit_log
    FOR VALUES FROM ('2024-05-01') TO ('2024-06-01');

CREATE TABLE cache_audit_log_2024_06 PARTITION OF cache_audit_log
    FOR VALUES FROM ('2024-06-01') TO ('2024-07-01');

CREATE TABLE cache_audit_log_2024_07 PARTITION OF cache_audit_log
    FOR VALUES FROM ('2024-07-01') TO ('2024-08-01');

CREATE TABLE cache_audit_log_2024_08 PARTITION OF cache_audit_log
    FOR VALUES FROM ('2024-08-01') TO ('2024-09-01');

CREATE TABLE cache_audit_log_2024_09 PARTITION OF cache_audit_log
    FOR VALUES FROM ('2024-09-01') TO ('2024-10-01');

CREATE TABLE cache_audit_log_2024_10 PARTITION OF cache_audit_log
    FOR VALUES FROM ('2024-10-01') TO ('2024-11-01');

CREATE TABLE cache_audit_log_2024_11 PARTITION OF cache_audit_log
    FOR VALUES FROM ('2024-11-01') TO ('2024-12-01');

CREATE TABLE cache_audit_log_2024_12 PARTITION OF cache_audit_log
    FOR VALUES FROM ('2024-12-01') TO ('2025-01-01');

-- Create default partition for future dates
CREATE TABLE cache_audit_log_default PARTITION OF cache_audit_log DEFAULT;

-- ============================================================================
-- PARTITION CACHE_STATISTICS BY TIME AND METRIC TYPE
-- ============================================================================

-- Create partitioned statistics table
DROP TABLE IF EXISTS cache_statistics CASCADE;

CREATE TABLE cache_statistics (
    id UUID DEFAULT uuid_generate_v4(),
    metric_name VARCHAR(100) NOT NULL,
    metric_type VARCHAR(50) NOT NULL CHECK (metric_type IN ('counter', 'gauge', 'histogram')),
    metric_value DECIMAL(20,6) NOT NULL,
    namespace VARCHAR(100) NOT NULL DEFAULT 'default',
    tags JSONB DEFAULT '{}'::jsonb,
    recorded_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    time_bucket VARCHAR(20) NOT NULL DEFAULT 'hour' CHECK (time_bucket IN ('minute', 'hour', 'day', 'week')),
    PRIMARY KEY (id, recorded_at, metric_type)
) PARTITION BY RANGE (recorded_at);

-- Create daily partitions for statistics (last 30 days + next 30 days)
-- This is more granular than audit log due to high volume of metrics

-- Function to create daily partitions
CREATE OR REPLACE FUNCTION create_daily_partition(
    table_name TEXT,
    start_date DATE
) RETURNS VOID AS $$
DECLARE
    partition_name TEXT;
    start_ts TIMESTAMP;
    end_ts TIMESTAMP;
BEGIN
    partition_name := table_name || '_' || to_char(start_date, 'YYYY_MM_DD');
    start_ts := start_date::TIMESTAMP;
    end_ts := (start_date + INTERVAL '1 day')::TIMESTAMP;
    
    EXECUTE format('CREATE TABLE %I PARTITION OF %I FOR VALUES FROM (%L) TO (%L)',
                   partition_name, table_name, start_ts, end_ts);
END;
$$ LANGUAGE plpgsql;

-- Create partitions for the last 30 days and next 30 days
DO $$
DECLARE
    i INTEGER;
    partition_date DATE;
BEGIN
    -- Last 30 days
    FOR i IN -30..30 LOOP
        partition_date := CURRENT_DATE + (i || ' days')::INTERVAL;
        PERFORM create_daily_partition('cache_statistics', partition_date);
    END LOOP;
END $$;

-- ============================================================================
-- PARTITION CACHE_ENTRIES BY NAMESPACE (Hash Partitioning)
-- ============================================================================

-- For very large cache deployments, consider hash partitioning by namespace
-- This is optional and should be evaluated based on usage patterns

-- Example hash partitioning (uncomment if needed):
/*
DROP TABLE IF EXISTS cache_entries CASCADE;

CREATE TABLE cache_entries (
    id UUID DEFAULT uuid_generate_v4(),
    cache_key VARCHAR(512) NOT NULL,
    data_value TEXT NOT NULL,
    data_type VARCHAR(100) NOT NULL DEFAULT 'json',
    compression_type VARCHAR(50) NOT NULL DEFAULT 'none',
    data_size_bytes BIGINT NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    access_count BIGINT NOT NULL DEFAULT 0,
    last_accessed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    namespace VARCHAR(100) NOT NULL DEFAULT 'default',
    tags JSONB DEFAULT '[]'::jsonb,
    is_compressed BOOLEAN NOT NULL DEFAULT FALSE,
    checksum VARCHAR(64),
    PRIMARY KEY (id, namespace),
    UNIQUE (cache_key, namespace)
) PARTITION BY HASH (namespace);

-- Create 4 hash partitions
CREATE TABLE cache_entries_0 PARTITION OF cache_entries FOR VALUES WITH (MODULUS 4, REMAINDER 0);
CREATE TABLE cache_entries_1 PARTITION OF cache_entries FOR VALUES WITH (MODULUS 4, REMAINDER 1);
CREATE TABLE cache_entries_2 PARTITION OF cache_entries FOR VALUES WITH (MODULUS 4, REMAINDER 2);
CREATE TABLE cache_entries_3 PARTITION OF cache_entries FOR VALUES WITH (MODULUS 4, REMAINDER 3);
*/

-- ============================================================================
-- AUTOMATIC PARTITION MANAGEMENT FUNCTIONS
-- ============================================================================

-- Function to create next month's audit log partition
CREATE OR REPLACE FUNCTION create_next_audit_partition() RETURNS VOID AS $$
DECLARE
    next_month DATE;
    partition_name TEXT;
    start_date TEXT;
    end_date TEXT;
BEGIN
    next_month := date_trunc('month', CURRENT_DATE + INTERVAL '1 month');
    partition_name := 'cache_audit_log_' || to_char(next_month, 'YYYY_MM');
    start_date := next_month::TEXT;
    end_date := (next_month + INTERVAL '1 month')::TEXT;
    
    EXECUTE format('CREATE TABLE %I PARTITION OF cache_audit_log FOR VALUES FROM (%L) TO (%L)',
                   partition_name, start_date, end_date);
    
    RAISE NOTICE 'Created partition: %', partition_name;
END;
$$ LANGUAGE plpgsql;

-- Function to drop old audit log partitions (older than 6 months)
CREATE OR REPLACE FUNCTION drop_old_audit_partitions() RETURNS VOID AS $$
DECLARE
    old_partition RECORD;
    cutoff_date DATE;
BEGIN
    cutoff_date := date_trunc('month', CURRENT_DATE - INTERVAL '6 months');
    
    FOR old_partition IN 
        SELECT schemaname, tablename 
        FROM pg_tables 
        WHERE schemaname = 'cache' 
        AND tablename LIKE 'cache_audit_log_____'
        AND tablename < 'cache_audit_log_' || to_char(cutoff_date, 'YYYY_MM')
    LOOP
        EXECUTE format('DROP TABLE %I.%I', old_partition.schemaname, old_partition.tablename);
        RAISE NOTICE 'Dropped old partition: %', old_partition.tablename;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to create next day's statistics partition
CREATE OR REPLACE FUNCTION create_next_stats_partition() RETURNS VOID AS $$
DECLARE
    next_date DATE;
BEGIN
    next_date := CURRENT_DATE + INTERVAL '1 day';
    PERFORM create_daily_partition('cache_statistics', next_date);
    RAISE NOTICE 'Created statistics partition for: %', next_date;
END;
$$ LANGUAGE plpgsql;

-- Function to drop old statistics partitions (older than 30 days)
CREATE OR REPLACE FUNCTION drop_old_stats_partitions() RETURNS VOID AS $$
DECLARE
    old_partition RECORD;
    cutoff_date DATE;
BEGIN
    cutoff_date := CURRENT_DATE - INTERVAL '30 days';
    
    FOR old_partition IN 
        SELECT schemaname, tablename 
        FROM pg_tables 
        WHERE schemaname = 'cache' 
        AND tablename LIKE 'cache_statistics_____'
        AND tablename < 'cache_statistics_' || to_char(cutoff_date, 'YYYY_MM_DD')
    LOOP
        EXECUTE format('DROP TABLE %I.%I', old_partition.schemaname, old_partition.tablename);
        RAISE NOTICE 'Dropped old statistics partition: %', old_partition.tablename;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- SCHEDULED PARTITION MAINTENANCE
-- ============================================================================

-- Note: These should be scheduled using pg_cron or external cron jobs

-- Example cron jobs (add to pg_cron if available):
-- SELECT cron.schedule('create-audit-partition', '0 0 1 * *', 'SELECT cache.create_next_audit_partition();');
-- SELECT cron.schedule('drop-old-audit-partitions', '0 1 1 * *', 'SELECT cache.drop_old_audit_partitions();');
-- SELECT cron.schedule('create-stats-partition', '0 23 * * *', 'SELECT cache.create_next_stats_partition();');
-- SELECT cron.schedule('drop-old-stats-partitions', '0 2 * * *', 'SELECT cache.drop_old_stats_partitions();');

-- ============================================================================
-- PARTITION PRUNING CONFIGURATION
-- ============================================================================

-- Enable constraint exclusion for better partition pruning
-- Add to postgresql.conf:
-- constraint_exclusion = partition
-- enable_partition_pruning = on
-- enable_partitionwise_join = on
-- enable_partitionwise_aggregate = on

-- Comments for documentation
COMMENT ON FUNCTION create_daily_partition IS 'Creates a daily partition for time-series tables';
COMMENT ON FUNCTION create_next_audit_partition IS 'Creates next month audit log partition';
COMMENT ON FUNCTION drop_old_audit_partitions IS 'Drops audit log partitions older than 6 months';
COMMENT ON FUNCTION create_next_stats_partition IS 'Creates next day statistics partition';
COMMENT ON FUNCTION drop_old_stats_partitions IS 'Drops statistics partitions older than 30 days';
