-- PostgreSQL Cache Schema for Vulnera System
-- Version: 1.0
-- Description: Initial cache tables and basic structure

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create cache schema
CREATE SCHEMA IF NOT EXISTS cache;

-- Set search path
SET search_path TO cache, public;

-- Cache entries table - Main cache storage
CREATE TABLE IF NOT EXISTS cache_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cache_key VARCHAR(512) NOT NULL UNIQUE,
    data_value TEXT NOT NULL,
    data_type VARCHAR(100) NOT NULL DEFAULT 'json',
    compression_type VARCHAR(50) NOT NULL DEFAULT 'none',
    data_size_bytes BIGINT NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    access_count BIGINT NOT NULL DEFAULT 0,
    last_accessed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    namespace VARCHAR(100) NOT NULL DEFAULT 'default',
    tags JSONB DEFAULT '[]'::jsonb,
    is_compressed BOOLEAN NOT NULL DEFAULT FALSE,
    checksum VARCHAR(64)
);

-- Cache metadata table - Namespace and policy management
CREATE TABLE IF NOT EXISTS cache_metadata (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    namespace VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    total_entries BIGINT NOT NULL DEFAULT 0,
    total_size_bytes BIGINT NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    retention_policy JSONB DEFAULT '{}'::jsonb,
    auto_cleanup_enabled BOOLEAN NOT NULL DEFAULT TRUE
);

-- Cache statistics table - Performance and usage metrics
CREATE TABLE IF NOT EXISTS cache_statistics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_name VARCHAR(100) NOT NULL,
    metric_type VARCHAR(50) NOT NULL CHECK (metric_type IN ('counter', 'gauge', 'histogram')),
    metric_value DECIMAL(20,6) NOT NULL,
    namespace VARCHAR(100) NOT NULL DEFAULT 'default',
    tags JSONB DEFAULT '{}'::jsonb,
    recorded_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    time_bucket VARCHAR(20) NOT NULL DEFAULT 'hour' CHECK (time_bucket IN ('minute', 'hour', 'day', 'week'))
);

-- Cache locks table - Distributed locking mechanism
CREATE TABLE IF NOT EXISTS cache_locks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lock_key VARCHAR(512) NOT NULL UNIQUE,
    owner_id VARCHAR(100) NOT NULL,
    acquired_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    lock_type VARCHAR(20) NOT NULL DEFAULT 'exclusive' CHECK (lock_type IN ('read', 'write', 'exclusive')),
    is_active BOOLEAN NOT NULL DEFAULT TRUE
);

-- Cache warming jobs table - Scheduled cache warming
CREATE TABLE IF NOT EXISTS cache_warming_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_name VARCHAR(200) NOT NULL UNIQUE,
    cache_pattern VARCHAR(500) NOT NULL,
    cron_schedule VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    last_run_at TIMESTAMP WITH TIME ZONE,
    next_run_at TIMESTAMP WITH TIME ZONE,
    job_config JSONB DEFAULT '{}'::jsonb,
    retry_count INTEGER NOT NULL DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Cache audit log table - Operation logging and auditing
CREATE TABLE IF NOT EXISTS cache_audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cache_key VARCHAR(512),
    operation VARCHAR(20) NOT NULL CHECK (operation IN ('get', 'set', 'delete', 'expire', 'clear')),
    user_id VARCHAR(100),
    session_id VARCHAR(100),
    operation_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    result VARCHAR(20) NOT NULL CHECK (result IN ('hit', 'miss', 'error', 'success')),
    response_time_ms BIGINT,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Comments for documentation
COMMENT ON TABLE cache_entries IS 'Main cache storage table with TTL and compression support';
COMMENT ON TABLE cache_metadata IS 'Namespace metadata and cache policies';
COMMENT ON TABLE cache_statistics IS 'Performance metrics and usage statistics';
COMMENT ON TABLE cache_locks IS 'Distributed locking for cache operations';
COMMENT ON TABLE cache_warming_jobs IS 'Scheduled cache warming job definitions';
COMMENT ON TABLE cache_audit_log IS 'Audit trail for all cache operations';

-- Column comments for key fields
COMMENT ON COLUMN cache_entries.cache_key IS 'Unique cache key with namespace prefix';
COMMENT ON COLUMN cache_entries.data_value IS 'Serialized cache data (JSON, binary, etc.)';
COMMENT ON COLUMN cache_entries.compression_type IS 'Compression algorithm used (gzip, lz4, none)';
COMMENT ON COLUMN cache_entries.expires_at IS 'Cache entry expiration timestamp (NULL = no expiry)';
COMMENT ON COLUMN cache_entries.tags IS 'JSON array of tags for cache categorization';
COMMENT ON COLUMN cache_metadata.retention_policy IS 'JSON policy for TTL and cleanup rules';
COMMENT ON COLUMN cache_statistics.time_bucket IS 'Time aggregation bucket for metrics';
COMMENT ON COLUMN cache_locks.lock_type IS 'Type of lock (read, write, exclusive)';
COMMENT ON COLUMN cache_warming_jobs.cache_pattern IS 'Pattern for cache keys to warm (supports wildcards)';
COMMENT ON COLUMN cache_audit_log.metadata IS 'Additional operation metadata and context';
