-- PostgreSQL Cache Indexes for Optimal Performance
-- Version: 1.0
-- Description: Comprehensive indexing strategy for cache tables

SET search_path TO cache, public;

-- ============================================================================
-- CACHE_ENTRIES TABLE INDEXES
-- ============================================================================

-- Primary lookup index - Most important for cache operations
CREATE UNIQUE INDEX IF NOT EXISTS idx_cache_entries_cache_key 
ON cache_entries (cache_key);

-- Expiration cleanup index - Critical for TTL operations
CREATE INDEX IF NOT EXISTS idx_cache_entries_expires_at 
ON cache_entries (expires_at) 
WHERE expires_at IS NOT NULL;

-- Namespace-based queries
CREATE INDEX IF NOT EXISTS idx_cache_entries_namespace 
ON cache_entries (namespace);

-- Namespace + expiration for efficient cleanup
CREATE INDEX IF NOT EXISTS idx_cache_entries_namespace_expires 
ON cache_entries (namespace, expires_at) 
WHERE expires_at IS NOT NULL;

-- Access pattern analysis
CREATE INDEX IF NOT EXISTS idx_cache_entries_last_accessed 
ON cache_entries (last_accessed_at DESC);

-- Size-based eviction support
CREATE INDEX IF NOT EXISTS idx_cache_entries_size_created 
ON cache_entries (data_size_bytes DESC, created_at ASC);

-- Tag-based queries (GIN index for JSONB)
CREATE INDEX IF NOT EXISTS idx_cache_entries_tags 
ON cache_entries USING GIN (tags);

-- Data type filtering
CREATE INDEX IF NOT EXISTS idx_cache_entries_data_type 
ON cache_entries (data_type);

-- Compression analysis
CREATE INDEX IF NOT EXISTS idx_cache_entries_compression 
ON cache_entries (compression_type, is_compressed);

-- Time-series analysis
CREATE INDEX IF NOT EXISTS idx_cache_entries_created_at 
ON cache_entries (created_at DESC);

-- Composite index for common queries
CREATE INDEX IF NOT EXISTS idx_cache_entries_namespace_type_created 
ON cache_entries (namespace, data_type, created_at DESC);

-- ============================================================================
-- CACHE_STATISTICS TABLE INDEXES
-- ============================================================================

-- Metric lookup by name and time
CREATE INDEX IF NOT EXISTS idx_cache_statistics_metric_time 
ON cache_statistics (metric_name, recorded_at DESC);

-- Namespace-based metrics
CREATE INDEX IF NOT EXISTS idx_cache_statistics_namespace_time 
ON cache_statistics (namespace, recorded_at DESC);

-- Time bucket aggregation
CREATE INDEX IF NOT EXISTS idx_cache_statistics_bucket_time 
ON cache_statistics (time_bucket, recorded_at DESC);

-- Metric type filtering
CREATE INDEX IF NOT EXISTS idx_cache_statistics_type_time 
ON cache_statistics (metric_type, recorded_at DESC);

-- Tags-based queries (GIN index for JSONB)
CREATE INDEX IF NOT EXISTS idx_cache_statistics_tags 
ON cache_statistics USING GIN (tags);

-- Composite index for dashboard queries
CREATE INDEX IF NOT EXISTS idx_cache_statistics_namespace_metric_bucket 
ON cache_statistics (namespace, metric_name, time_bucket, recorded_at DESC);

-- ============================================================================
-- CACHE_LOCKS TABLE INDEXES
-- ============================================================================

-- Primary lock lookup
CREATE UNIQUE INDEX IF NOT EXISTS idx_cache_locks_lock_key 
ON cache_locks (lock_key);

-- Active locks filtering
CREATE INDEX IF NOT EXISTS idx_cache_locks_active_expires 
ON cache_locks (is_active, expires_at) 
WHERE is_active = TRUE;

-- Owner-based queries
CREATE INDEX IF NOT EXISTS idx_cache_locks_owner 
ON cache_locks (owner_id, acquired_at DESC);

-- Lock type analysis
CREATE INDEX IF NOT EXISTS idx_cache_locks_type_active 
ON cache_locks (lock_type, is_active);

-- Cleanup expired locks
CREATE INDEX IF NOT EXISTS idx_cache_locks_expires_at 
ON cache_locks (expires_at) 
WHERE expires_at < NOW();

-- ============================================================================
-- CACHE_WARMING_JOBS TABLE INDEXES
-- ============================================================================

-- Job name lookup
CREATE UNIQUE INDEX IF NOT EXISTS idx_cache_warming_jobs_name 
ON cache_warming_jobs (job_name);

-- Status-based queries
CREATE INDEX IF NOT EXISTS idx_cache_warming_jobs_status 
ON cache_warming_jobs (status, next_run_at);

-- Scheduling index
CREATE INDEX IF NOT EXISTS idx_cache_warming_jobs_next_run 
ON cache_warming_jobs (next_run_at) 
WHERE status IN ('pending', 'failed') AND next_run_at IS NOT NULL;

-- Pattern-based searches
CREATE INDEX IF NOT EXISTS idx_cache_warming_jobs_pattern 
ON cache_warming_jobs (cache_pattern);

-- Job monitoring
CREATE INDEX IF NOT EXISTS idx_cache_warming_jobs_last_run 
ON cache_warming_jobs (last_run_at DESC);

-- ============================================================================
-- CACHE_AUDIT_LOG TABLE INDEXES
-- ============================================================================

-- Time-series queries (most common)
CREATE INDEX IF NOT EXISTS idx_cache_audit_log_operation_at 
ON cache_audit_log (operation_at DESC);

-- Cache key analysis
CREATE INDEX IF NOT EXISTS idx_cache_audit_log_cache_key_time 
ON cache_audit_log (cache_key, operation_at DESC);

-- Operation type analysis
CREATE INDEX IF NOT EXISTS idx_cache_audit_log_operation_time 
ON cache_audit_log (operation, operation_at DESC);

-- Result analysis (hit/miss rates)
CREATE INDEX IF NOT EXISTS idx_cache_audit_log_result_time 
ON cache_audit_log (result, operation_at DESC);

-- User activity tracking
CREATE INDEX IF NOT EXISTS idx_cache_audit_log_user_time 
ON cache_audit_log (user_id, operation_at DESC) 
WHERE user_id IS NOT NULL;

-- Session tracking
CREATE INDEX IF NOT EXISTS idx_cache_audit_log_session_time 
ON cache_audit_log (session_id, operation_at DESC) 
WHERE session_id IS NOT NULL;

-- Performance analysis
CREATE INDEX IF NOT EXISTS idx_cache_audit_log_response_time 
ON cache_audit_log (response_time_ms DESC) 
WHERE response_time_ms IS NOT NULL;

-- Metadata queries (GIN index for JSONB)
CREATE INDEX IF NOT EXISTS idx_cache_audit_log_metadata 
ON cache_audit_log USING GIN (metadata);

-- ============================================================================
-- CACHE_METADATA TABLE INDEXES
-- ============================================================================

-- Namespace lookup (already unique)
CREATE UNIQUE INDEX IF NOT EXISTS idx_cache_metadata_namespace 
ON cache_metadata (namespace);

-- Size analysis
CREATE INDEX IF NOT EXISTS idx_cache_metadata_size 
ON cache_metadata (total_size_bytes DESC);

-- Entry count analysis
CREATE INDEX IF NOT EXISTS idx_cache_metadata_entries 
ON cache_metadata (total_entries DESC);

-- Auto-cleanup filtering
CREATE INDEX IF NOT EXISTS idx_cache_metadata_auto_cleanup 
ON cache_metadata (auto_cleanup_enabled, updated_at);

-- Retention policy queries (GIN index for JSONB)
CREATE INDEX IF NOT EXISTS idx_cache_metadata_retention_policy 
ON cache_metadata USING GIN (retention_policy);

-- ============================================================================
-- PARTIAL INDEXES FOR SPECIFIC USE CASES
-- ============================================================================

-- Only expired entries for cleanup
CREATE INDEX IF NOT EXISTS idx_cache_entries_expired_only 
ON cache_entries (expires_at, namespace) 
WHERE expires_at < NOW();

-- Only active locks
CREATE INDEX IF NOT EXISTS idx_cache_locks_active_only 
ON cache_locks (lock_key, expires_at) 
WHERE is_active = TRUE;

-- Only pending/failed warming jobs
CREATE INDEX IF NOT EXISTS idx_cache_warming_jobs_runnable 
ON cache_warming_jobs (next_run_at, retry_count) 
WHERE status IN ('pending', 'failed');

-- Recent audit entries (last 30 days)
CREATE INDEX IF NOT EXISTS idx_cache_audit_log_recent 
ON cache_audit_log (operation_at DESC, operation, result) 
WHERE operation_at > NOW() - INTERVAL '30 days';

-- ============================================================================
-- ANALYZE TABLES FOR OPTIMAL QUERY PLANNING
-- ============================================================================

ANALYZE cache_entries;
ANALYZE cache_metadata;
ANALYZE cache_statistics;
ANALYZE cache_locks;
ANALYZE cache_warming_jobs;
ANALYZE cache_audit_log;
