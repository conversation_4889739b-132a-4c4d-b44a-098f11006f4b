# Database Schema and Migrations

This directory contains all database-related files for the PostgreSQL cache system.

## Directory Structure

```
database/
├── README.md                    # This file
├── schema/                      # Database schema definitions
│   ├── 001_initial_cache_tables.sql
│   ├── 002_cache_indexes.sql
│   ├── 003_cache_partitions.sql
│   └── 004_cache_functions.sql
├── migrations/                  # Database migrations
│   ├── up/                      # Migration up scripts
│   └── down/                    # Migration down scripts
├── seeds/                       # Initial data and test data
│   ├── cache_metadata.sql
│   └── test_data.sql
├── procedures/                  # Stored procedures and functions
│   ├── cache_cleanup.sql
│   ├── cache_statistics.sql
│   └── cache_warming.sql
├── views/                       # Database views
│   ├── cache_analytics.sql
│   └── cache_health.sql
├── triggers/                    # Database triggers
│   ├── cache_audit_trigger.sql
│   └── cache_stats_trigger.sql
└── docker/                      # Docker setup for PostgreSQL
    ├── Dockerfile
    ├── docker-compose.yml
    ├── init.sql
    └── postgresql.conf
```

## Usage

### 1. Development Setup
```bash
# Start PostgreSQL with Docker
cd database/docker
docker-compose up -d

# Run initial schema
psql -h localhost -U vulnera_user -d vulnera_cache -f ../schema/001_initial_cache_tables.sql
```

### 2. Production Deployment
```bash
# Run all schema files in order
for file in database/schema/*.sql; do
    psql -h $DB_HOST -U $DB_USER -d $DB_NAME -f "$file"
done
```

### 3. Migrations
```bash
# Apply migrations
for file in database/migrations/up/*.sql; do
    psql -h $DB_HOST -U $DB_USER -d $DB_NAME -f "$file"
done
```

## Environment Variables

- `POSTGRES_HOST`: Database host (default: localhost)
- `POSTGRES_PORT`: Database port (default: 5432)
- `POSTGRES_DB`: Database name (default: vulnera_cache)
- `POSTGRES_USER`: Database user (default: vulnera_user)
- `POSTGRES_PASSWORD`: Database password
