# Sample requirements.txt file for testing the vulnerability analyzer
# This file contains a mix of packages with known vulnerabilities and safe packages

# Web frameworks
django==3.2.0
flask==1.1.0
fastapi==0.68.0

# Data processing
pandas==1.3.0
numpy==1.21.0
requests==2.25.0

# Security and cryptography
cryptography==3.0.0
pyjwt==1.7.0
pillow==8.0.0

# Development tools
pytest==6.2.0
black==21.5b0
flake8==3.9.0

# Utilities
pyyaml==5.4.0
click==7.1.2
jinja2==2.11.0

# Package with no version specified
setuptools

# Newer packages (likely safe)
httpx==0.24.0
pydantic==1.10.0
