#!/usr/bin/env python3
"""
Dependency Vulnerability Analyzer

A comprehensive tool for analyzing dependency files to identify vulnerabilities
and suggest package updates using the OSV (Open Source Vulnerability) API.

License: MIT
"""

import argparse
import asyncio
import hashlib
import json
import os
import re
import sys
import time
import webbrowser
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from urllib.parse import quote

import aiohttp
from tqdm.asyncio import tqdm

# Constants
OSV_API_BASE = "https://api.osv.dev/v1"
PYPI_API_BASE = "https://pypi.org/pypi"
REQUEST_DELAY = 0.1  # seconds between requests
CACHE_DIR = ".vulnera_cache"
CACHE_EXPIRY_HOURS = 24
USER_AGENT = "Dependency-Vulnerability-Analyzer/2.0"


# Custom exceptions
class VulnerabilityAnalysisError(Exception):
    """Base exception for vulnerability analysis errors."""

    pass


class APIError(VulnerabilityAnalysisError):
    """Exception raised for API-related errors."""

    pass


class Package:
    """Represents a package with its name and version."""

    def __init__(self, name: str, version: str):
        self.name = name.strip()
        self.version = version.strip()
        self.vulnerabilities = []
        self.latest_version = None
        self.recommended_version = None


class VulnerabilityAnalyzer:
    """Main class for analyzing dependencies and finding vulnerabilities."""

    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or os.environ.get("VULNERABILITY_API_KEY")
        self.session_headers = {
            "User-Agent": USER_AGENT,
            "Content-Type": "application/json",
        }

        # Initialize cache directory
        self.cache_dir = Path(CACHE_DIR)
        self.cache_dir.mkdir(exist_ok=True)

        # Session for reuse
        self.session: Optional[aiohttp.ClientSession] = None

    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            headers=self.session_headers, timeout=aiohttp.ClientTimeout(total=30)
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()

    def _get_cache_key(self, url: str, data: Optional[Dict] = None) -> str:
        """Generate a cache key for the request."""
        cache_string = f"{url}_{json.dumps(data, sort_keys=True) if data else ''}"
        return hashlib.md5(cache_string.encode()).hexdigest()

    def _get_cache_path(self, cache_key: str) -> Path:
        """Get the cache file path for a given cache key."""
        return self.cache_dir / f"{cache_key}.json"

    def _is_cache_valid(self, cache_path: Path) -> bool:
        """Check if cache file exists and is not expired."""
        if not cache_path.exists():
            return False

        cache_time = datetime.fromtimestamp(cache_path.stat().st_mtime)
        expiry_time = cache_time + timedelta(hours=CACHE_EXPIRY_HOURS)
        return datetime.now() < expiry_time

    def _read_cache(self, cache_key: str) -> Optional[Dict]:
        """Read data from cache if valid."""
        cache_path = self._get_cache_path(cache_key)
        if self._is_cache_valid(cache_path):
            try:
                with open(cache_path, "r", encoding="utf-8") as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                # Invalid cache file, remove it
                cache_path.unlink(missing_ok=True)
        return None

    def _write_cache(self, cache_key: str, data: Dict) -> None:
        """Write data to cache."""
        cache_path = self._get_cache_path(cache_key)
        try:
            with open(cache_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2)
        except IOError:
            # Cache write failed, continue without caching
            pass

    async def _make_request(self, url: str, data: Optional[Dict] = None) -> Dict:
        """Make an HTTP request with caching and error handling."""
        if not self.session:
            raise APIError("Session not initialized. Use async context manager.")

        # Check cache first
        cache_key = self._get_cache_key(url, data)
        cached_data = self._read_cache(cache_key)
        if cached_data is not None:
            return cached_data

        # Rate limiting
        await asyncio.sleep(REQUEST_DELAY)

        try:
            if data:
                # POST request
                async with self.session.post(url, json=data) as response:
                    response.raise_for_status()
                    result = await response.json()
            else:
                # GET request
                async with self.session.get(url) as response:
                    response.raise_for_status()
                    result = await response.json()

            # Cache the result
            self._write_cache(cache_key, result)
            return result

        except aiohttp.ClientResponseError as e:
            if e.status == 404:
                # Cache 404 responses to avoid repeated requests
                empty_result = {}
                self._write_cache(cache_key, empty_result)
                return empty_result
            raise APIError(f"HTTP Error {e.status}: {e.message} for URL: {url}")
        except aiohttp.ClientError as e:
            raise APIError(f"Request failed: {e} for URL: {url}")
        except json.JSONDecodeError:
            raise APIError(f"Invalid JSON response from: {url}")
        except Exception as e:
            raise APIError(f"Unexpected error: {e} for URL: {url}")

    def parse_requirements_file(self, file_path: str) -> List[Package]:
        """Parse a requirements.txt file and extract packages with versions."""
        packages = []

        try:
            with open(file_path, "r", encoding="utf-8") as file:
                for line_num, line in enumerate(file, 1):
                    line = line.strip()

                    # Skip empty lines and comments
                    if not line or line.startswith("#"):
                        continue

                    # Handle various requirement formats
                    package = self._parse_requirement_line(line, line_num)
                    if package:
                        packages.append(package)

        except FileNotFoundError:
            print(f"Error: File '{file_path}' not found.")
            sys.exit(1)
        except IOError as e:
            print(f"Error reading file '{file_path}': {e}")
            sys.exit(1)

        return packages

    def _parse_requirement_line(self, line: str, line_num: int) -> Optional[Package]:
        """Parse a single requirement line and extract package name and version."""
        # Remove inline comments
        line = line.split("#")[0].strip()

        # Skip git URLs and other special cases
        if any(
            prefix in line
            for prefix in ["git+", "http://", "https://", "file://", "-e "]
        ):
            print(
                f"Warning: Skipping non-standard requirement at line {line_num}: {line}"
            )
            return None

        # Handle different version specifiers (==, >=, >, <=, <, ~=, !=)
        version_patterns = [
            r"^([a-zA-Z0-9_.-]+)==([0-9][a-zA-Z0-9._-]*).*$",  # Exact version
            r"^([a-zA-Z0-9_.-]+)>=([0-9][a-zA-Z0-9._-]*).*$",  # Minimum version
            r"^([a-zA-Z0-9_.-]+)>([0-9][a-zA-Z0-9._-]*).*$",  # Greater than
            r"^([a-zA-Z0-9_.-]+)~=([0-9][a-zA-Z0-9._-]*).*$",  # Compatible release
            r"^([a-zA-Z0-9_.-]+)<=([0-9][a-zA-Z0-9._-]*).*$",  # Less than or equal
            r"^([a-zA-Z0-9_.-]+)<([0-9][a-zA-Z0-9._-]*).*$",  # Less than
        ]

        for pattern in version_patterns:
            match = re.match(pattern, line)
            if match:
                name, version = match.groups()
                return Package(name, version)

        # Handle package name only (no version specified)
        name_only_match = re.match(r"^([a-zA-Z0-9_.-]+)$", line)
        if name_only_match:
            name = name_only_match.group(1)
            print(
                f"Warning: No version specified for '{name}' at line {line_num}. Using 'latest'."
            )
            return Package(name, "latest")

        print(f"Warning: Could not parse requirement at line {line_num}: {line}")
        return None

    async def check_vulnerabilities(self, package: Package) -> None:
        """Check for vulnerabilities in a package using the OSV API."""
        # Prepare the query for OSV API
        query_data: Dict[str, Any] = {
            "package": {"name": package.name, "ecosystem": "PyPI"}
        }

        # Add version if it's not 'latest'
        if package.version != "latest":
            query_data["version"] = package.version

        url = f"{OSV_API_BASE}/query"
        response = await self._make_request(url, query_data)

        if response and "vulns" in response:
            for vuln in response["vulns"]:
                vulnerability_info = {
                    "id": vuln.get("id", "Unknown"),
                    "summary": vuln.get("summary", "No summary available"),
                    "severity": self._extract_severity(vuln),
                    "fixed_versions": self._extract_fixed_versions(vuln),
                    "references": [
                        ref.get("url", "") for ref in vuln.get("references", [])
                    ],
                }
                package.vulnerabilities.append(vulnerability_info)

    def _extract_severity(self, vuln: Dict) -> str:
        """Extract severity information from vulnerability data."""
        severity = vuln.get("database_specific", {}).get("severity")
        if severity:
            return severity

        # Try to extract from CVSS score if available
        for item in vuln.get("severity", []):
            if item.get("type") == "CVSS_V3":
                score = item.get("score")
                if score:
                    try:
                        score_float = float(score)
                        if score_float >= 9.0:
                            return "CRITICAL"
                        elif score_float >= 7.0:
                            return "HIGH"
                        elif score_float >= 4.0:
                            return "MEDIUM"
                        else:
                            return "LOW"
                    except (ValueError, TypeError):
                        pass

        return "UNKNOWN"

    def _extract_fixed_versions(self, vuln: Dict) -> List[str]:
        """Extract fixed versions from vulnerability data."""
        fixed_versions = []

        for affected in vuln.get("affected", []):
            if affected.get("package", {}).get("ecosystem") == "PyPI":
                for event in affected.get("database_specific", {}).get("ranges", []):
                    if "fixed" in event:
                        fixed_versions.append(event["fixed"])

        return fixed_versions

    async def get_latest_version(self, package: Package) -> None:
        """Get the latest version of a package from PyPI."""
        url = f"{PYPI_API_BASE}/{quote(package.name)}/json"
        response = await self._make_request(url)

        if response and "info" in response:
            package.latest_version = response["info"].get("version", "Unknown")

            # Determine recommended version
            if package.vulnerabilities:
                # If there are vulnerabilities, recommend the latest version
                # unless specific fixed versions are mentioned
                fixed_versions = []
                for vuln in package.vulnerabilities:
                    fixed_versions.extend(vuln.get("fixed_versions", []))

                if fixed_versions:
                    # Use the latest fixed version (assuming they are in chronological order)
                    package.recommended_version = (
                        fixed_versions[-1] if fixed_versions else package.latest_version
                    )
                else:
                    package.recommended_version = package.latest_version
            else:
                # No vulnerabilities, but still recommend latest if current is outdated
                if (
                    package.version != "latest"
                    and package.version != package.latest_version
                ):
                    package.recommended_version = package.latest_version

    async def analyze_package(self, package: Package) -> None:
        """Analyze a single package for vulnerabilities and updates."""
        await self.check_vulnerabilities(package)
        await self.get_latest_version(package)

    async def analyze_packages_concurrent(self, packages: List[Package]) -> None:
        """Analyze multiple packages concurrently with progress tracking."""
        # Use a simple approach with tqdm for now
        tasks = [self.analyze_package(package) for package in packages]

        # Execute with progress bar
        for i, task in enumerate(asyncio.as_completed(tasks)):
            try:
                await task
                print(f"[{i+1}/{len(packages)}] Completed analysis")
            except Exception as e:
                print(f"Error during analysis: {e}")
                continue

    def generate_report(
        self, packages: List[Package], output_format: str = "text"
    ) -> str:
        """Generate a vulnerability report in the specified format."""
        if output_format.lower() == "html":
            return self._generate_html_report(packages)
        else:
            return self._generate_text_report(packages)

    def _generate_text_report(self, packages: List[Package]) -> str:
        """Generate a concise, readable plain text report."""
        report_lines = []

        # Header
        report_lines.append("┌" + "─" * 78 + "┐")
        report_lines.append(
            "│" + " DEPENDENCY VULNERABILITY ANALYSIS REPORT".center(78) + "│"
        )
        report_lines.append("└" + "─" * 78 + "┘")
        report_lines.append("")

        # Summary stats
        vulnerable_packages = [p for p in packages if p.vulnerabilities]
        outdated_packages = [
            p
            for p in packages
            if p.recommended_version and p.version != p.recommended_version
        ]
        critical_vulns = sum(
            1
            for p in vulnerable_packages
            for v in p.vulnerabilities
            if v["severity"] == "CRITICAL"
        )
        high_vulns = sum(
            1
            for p in vulnerable_packages
            for v in p.vulnerabilities
            if v["severity"] == "HIGH"
        )

        report_lines.append(f"📊 SUMMARY")
        report_lines.append(f"   Total packages: {len(packages)}")
        report_lines.append(f"   🔴 Critical vulnerabilities: {critical_vulns}")
        report_lines.append(f"   🟠 High vulnerabilities: {high_vulns}")
        report_lines.append(
            f"   ⚠️  Packages with vulnerabilities: {len(vulnerable_packages)}"
        )
        report_lines.append(f"   📦 Packages needing updates: {len(outdated_packages)}")
        report_lines.append("")

        # Quick action items
        critical_packages = []
        if vulnerable_packages or outdated_packages:
            report_lines.append("🎯 QUICK ACTIONS NEEDED")
            report_lines.append("─" * 40)

            # Show only critical and high severity packages
            critical_packages = [
                p
                for p in vulnerable_packages
                if any(v["severity"] in ["CRITICAL", "HIGH"] for v in p.vulnerabilities)
            ]

            if critical_packages:
                report_lines.append("🚨 URGENT - Critical/High Risk Packages:")
                for package in critical_packages[:5]:  # Show top 5
                    severity_counts = {}
                    for vuln in package.vulnerabilities:
                        severity = vuln["severity"]
                        severity_counts[severity] = severity_counts.get(severity, 0) + 1

                    severity_summary = []
                    if "CRITICAL" in severity_counts:
                        severity_summary.append(
                            f"🔴{severity_counts['CRITICAL']} Critical"
                        )
                    if "HIGH" in severity_counts:
                        severity_summary.append(f"🟠{severity_counts['HIGH']} High")

                    report_lines.append(
                        f"   {package.name:20} {package.version:15} → {package.recommended_version or 'latest':15} ({', '.join(severity_summary)})"
                    )

                if len(critical_packages) > 5:
                    report_lines.append(
                        f"   ... and {len(critical_packages) - 5} more critical packages"
                    )
                report_lines.append("")

            # Show packages that just need updates (no vulnerabilities)
            update_only = [p for p in outdated_packages if not p.vulnerabilities]
            if update_only:
                report_lines.append("📦 Packages with available updates:")
                for package in update_only[:8]:  # Show top 8
                    report_lines.append(
                        f"   {package.name:20} {package.version:15} → {package.recommended_version:15}"
                    )

                if len(update_only) > 8:
                    report_lines.append(
                        f"   ... and {len(update_only) - 8} more packages"
                    )
                report_lines.append("")

        # Commands to fix issues
        if vulnerable_packages or outdated_packages:
            report_lines.append("💡 SUGGESTED COMMANDS")
            report_lines.append("─" * 40)

            # Generate pip install commands for vulnerable packages
            if critical_packages:
                upgrade_commands = []
                for package in critical_packages[:10]:  # Top 10 most critical
                    if package.recommended_version:
                        upgrade_commands.append(
                            f"{package.name}=={package.recommended_version}"
                        )

                if upgrade_commands:
                    report_lines.append("Fix critical vulnerabilities:")
                    report_lines.append(
                        f"   pip install --upgrade {' '.join(upgrade_commands)}"
                    )
                    report_lines.append("")

            report_lines.append("Update all packages:")
            report_lines.append(
                "   pip install --upgrade "
                + " ".join([p.name for p in outdated_packages[:15]])
            )
            report_lines.append("")

        # Detailed view (only for packages with issues)
        problematic_packages = [
            p
            for p in packages
            if p.vulnerabilities
            or (p.recommended_version and p.version != p.recommended_version)
        ]

        if problematic_packages:
            report_lines.append("📋 DETAILED ANALYSIS")
            report_lines.append("─" * 40)

            for package in problematic_packages:
                status_icon = (
                    "🔴"
                    if any(
                        v["severity"] in ["CRITICAL", "HIGH"]
                        for v in package.vulnerabilities
                    )
                    else "🟡" if package.vulnerabilities else "📦"
                )

                report_lines.append(f"{status_icon} {package.name}")
                report_lines.append(
                    f"   Current: {package.version} | Latest: {package.latest_version or 'Unknown'}"
                )

                if package.vulnerabilities:
                    # Group vulnerabilities by severity
                    severity_groups = {}
                    for vuln in package.vulnerabilities:
                        severity = vuln["severity"]
                        if severity not in severity_groups:
                            severity_groups[severity] = []
                        severity_groups[severity].append(vuln)

                    # Show summary by severity
                    severity_summary = []
                    for severity in ["CRITICAL", "HIGH", "MEDIUM", "LOW", "UNKNOWN"]:
                        if severity in severity_groups:
                            count = len(severity_groups[severity])
                            emoji = {
                                "CRITICAL": "🔴",
                                "HIGH": "🟠",
                                "MEDIUM": "🟡",
                                "LOW": "🔵",
                                "UNKNOWN": "⚪",
                            }
                            severity_summary.append(
                                f"{emoji[severity]}{count} {severity.lower()}"
                            )

                    report_lines.append(
                        f"   Vulnerabilities: {', '.join(severity_summary)}"
                    )

                    # Show a few critical/high examples
                    critical_high = [
                        v
                        for v in package.vulnerabilities
                        if v["severity"] in ["CRITICAL", "HIGH"]
                    ]
                    if critical_high:
                        for vuln in critical_high[:3]:  # Show top 3
                            summary = (
                                vuln["summary"][:60] + "..."
                                if len(vuln["summary"]) > 60
                                else vuln["summary"]
                            )
                            report_lines.append(
                                f"   • {vuln['id']} ({vuln['severity']}): {summary}"
                            )
                        if len(critical_high) > 3:
                            report_lines.append(
                                f"   • ... and {len(critical_high) - 3} more critical/high vulnerabilities"
                            )

                if (
                    package.recommended_version
                    and package.version != package.recommended_version
                ):
                    report_lines.append(
                        f"   💡 Recommended: {package.recommended_version}"
                    )

                report_lines.append("")

        # Footer
        report_lines.append("─" * 80)
        report_lines.append(
            f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | Use --format html for detailed web report"
        )

        return "\n".join(report_lines)

    def _generate_html_report(self, packages: List[Package]) -> str:
        """Generate an enhanced HTML report."""
        vulnerable_packages = [p for p in packages if p.vulnerabilities]
        critical_vulns = sum(
            1
            for p in vulnerable_packages
            for v in p.vulnerabilities
            if v["severity"] == "CRITICAL"
        )
        high_vulns = sum(
            1
            for p in vulnerable_packages
            for v in p.vulnerabilities
            if v["severity"] == "HIGH"
        )

        html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vulnerability Analysis Report</title>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        body {{ 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            background: #f5f7fa;
        }}
        .container {{ max-width: 1200px; margin: 0 auto; padding: 20px; }}
        .header {{ 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 40px 20px; 
            border-radius: 10px; 
            margin-bottom: 30px; 
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }}
        .header h1 {{ font-size: 2.5em; margin-bottom: 10px; }}
        .header p {{ font-size: 1.1em; opacity: 0.9; }}
        .stats {{ 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 20px; 
            margin-bottom: 30px; 
        }}
        .stat-card {{ 
            background: white; 
            padding: 25px; 
            border-radius: 10px; 
            text-align: center; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
        }}
        .stat-card.critical {{ border-left-color: #dc3545; }}
        .stat-card.high {{ border-left-color: #fd7e14; }}
        .stat-card.warning {{ border-left-color: #ffc107; }}
        .stat-number {{ font-size: 2.5em; font-weight: bold; margin-bottom: 5px; }}
        .stat-label {{ color: #666; font-size: 0.9em; text-transform: uppercase; letter-spacing: 1px; }}
        .packages {{ display: grid; gap: 20px; }}
        .package {{ 
            background: white; 
            border-radius: 10px; 
            overflow: hidden; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }}
        .package:hover {{ transform: translateY(-2px); }}
        .package-header {{ 
            padding: 20px; 
            font-weight: 600; 
            font-size: 1.1em;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        .package.critical .package-header {{ background: #fff5f5; border-left: 4px solid #dc3545; }}
        .package.high .package-header {{ background: #fff8f5; border-left: 4px solid #fd7e14; }}
        .package.medium .package-header {{ background: #fffbf0; border-left: 4px solid #ffc107; }}
        .package.safe .package-header {{ background: #f0fff4; border-left: 4px solid #28a745; }}
        .package-content {{ padding: 20px; }}
        .version-info {{ font-size: 0.9em; color: #666; }}
        .vulnerabilities {{ margin-top: 15px; }}
        .vulnerability {{ 
            margin: 10px 0; 
            padding: 15px; 
            border-radius: 8px; 
            border-left: 4px solid #ddd;
        }}
        .vulnerability.critical {{ background: #fff5f5; border-left-color: #dc3545; }}
        .vulnerability.high {{ background: #fff8f5; border-left-color: #fd7e14; }}
        .vulnerability.medium {{ background: #fffbf0; border-left-color: #ffc107; }}
        .vulnerability.moderate {{ background: #fffbf0; border-left-color: #ffc107; }}
        .vulnerability.low {{ background: #f0f9ff; border-left-color: #0ea5e9; }}
        .vuln-id {{ font-weight: 600; color: #333; }}
        .vuln-severity {{ 
            display: inline-block; 
            padding: 2px 8px; 
            border-radius: 12px; 
            font-size: 0.8em; 
            font-weight: 600; 
            text-transform: uppercase;
            margin-left: 10px;
        }}
        .severity-critical {{ background: #dc3545; color: white; }}
        .severity-high {{ background: #fd7e14; color: white; }}
        .severity-medium, .severity-moderate {{ background: #ffc107; color: #000; }}
        .severity-low {{ background: #0ea5e9; color: white; }}
        .severity-unknown {{ background: #6c757d; color: white; }}
        .vuln-summary {{ margin: 10px 0; color: #555; }}
        .recommendation {{ 
            background: #e7f3ff; 
            padding: 15px; 
            border-radius: 8px; 
            margin-top: 15px; 
            border-left: 4px solid #0ea5e9;
        }}
        .no-vulns {{ color: #28a745; font-weight: 500; }}
        .status-icon {{ font-size: 1.2em; margin-right: 10px; }}
        @media (max-width: 768px) {{
            .container {{ padding: 10px; }}
            .header {{ padding: 20px 10px; }}
            .header h1 {{ font-size: 2em; }}
            .stats {{ grid-template-columns: 1fr; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ Security Analysis Report</h1>
            <p>Generated on {datetime.now().strftime('%B %d, %Y at %H:%M:%S')}</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{len(packages)}</div>
                <div class="stat-label">Total Packages</div>
            </div>
            <div class="stat-card critical">
                <div class="stat-number">{critical_vulns}</div>
                <div class="stat-label">Critical Vulnerabilities</div>
            </div>
            <div class="stat-card high">
                <div class="stat-number">{high_vulns}</div>
                <div class="stat-label">High Risk Vulnerabilities</div>
            </div>
            <div class="stat-card warning">
                <div class="stat-number">{len(vulnerable_packages)}</div>
                <div class="stat-label">Vulnerable Packages</div>
            </div>
        </div>

        <div class="packages">
"""

        for package in packages:
            # Determine package risk level
            critical_count = sum(
                1 for v in package.vulnerabilities if v["severity"] == "CRITICAL"
            )
            high_count = sum(
                1 for v in package.vulnerabilities if v["severity"] == "HIGH"
            )

            if critical_count > 0:
                risk_class = "critical"
                status_icon = "🔴"
            elif high_count > 0:
                risk_class = "high"
                status_icon = "🟠"
            elif package.vulnerabilities:
                risk_class = "medium"
                status_icon = "🟡"
            else:
                risk_class = "safe"
                status_icon = "✅"

            html += f"""
            <div class="package {risk_class}">
                <div class="package-header">
                    <span><span class="status-icon">{status_icon}</span>{package.name}</span>
                    <div class="version-info">
                        Current: <strong>{package.version}</strong>
                        {f" | Latest: <strong>{package.latest_version}</strong>" if package.latest_version else ""}
                    </div>
                </div>
                <div class="package-content">
"""

            if package.vulnerabilities:
                # Group vulnerabilities by severity
                severity_groups = {}
                for vuln in package.vulnerabilities:
                    severity = vuln["severity"]
                    if severity not in severity_groups:
                        severity_groups[severity] = []
                    severity_groups[severity].append(vuln)

                html += "<div class='vulnerabilities'>"
                html += (
                    f"<h4>🚨 {len(package.vulnerabilities)} Vulnerabilities Found</h4>"
                )

                # Show severity summary
                severity_summary = []
                for severity in [
                    "CRITICAL",
                    "HIGH",
                    "MEDIUM",
                    "MODERATE",
                    "LOW",
                    "UNKNOWN",
                ]:
                    if severity in severity_groups:
                        count = len(severity_groups[severity])
                        severity_summary.append(
                            f"<span class='vuln-severity severity-{severity.lower()}'>{count} {severity.lower()}</span>"
                        )

                if severity_summary:
                    html += f"<p>Severity breakdown: {' '.join(severity_summary)}</p>"

                # Show some example vulnerabilities
                shown_vulns = 0
                for severity in [
                    "CRITICAL",
                    "HIGH",
                    "MEDIUM",
                    "MODERATE",
                    "LOW",
                    "UNKNOWN",
                ]:
                    if severity in severity_groups and shown_vulns < 5:
                        for vuln in severity_groups[severity][
                            :3
                        ]:  # Show up to 3 per severity
                            if shown_vulns >= 5:
                                break
                            severity_class = severity.lower()
                            html += f"""
                <div class="vulnerability {severity_class}">
                    <div class="vuln-id">{vuln['id']}<span class="vuln-severity severity-{severity_class}">{vuln['severity']}</span></div>
                    <div class="vuln-summary">{vuln['summary']}</div>
                    {f"<div><strong>Fixed in:</strong> {', '.join(vuln['fixed_versions'])}</div>" if vuln['fixed_versions'] else ""}
                </div>
"""
                            shown_vulns += 1

                if len(package.vulnerabilities) > shown_vulns:
                    html += f"<p><em>... and {len(package.vulnerabilities) - shown_vulns} more vulnerabilities</em></p>"

                html += "</div>"
            else:
                html += "<div class='no-vulns'>✅ No known vulnerabilities found</div>"

            if (
                package.recommended_version
                and package.version != package.recommended_version
            ):
                html += f"""
                <div class="recommendation">
                    <strong>💡 Recommendation:</strong> Update to version <strong>{package.recommended_version}</strong>
                </div>
"""

            html += """
                </div>
            </div>
"""

        html += """
        </div>
    </div>
</body>
</html>
"""
        return html


async def analyze_dependencies(args) -> None:
    """Async function to run the vulnerability analyzer."""
    print(f"Starting vulnerability analysis for: {args.file_path}")
    print("This may take a few minutes depending on the number of packages...")
    print()

    async with VulnerabilityAnalyzer(api_key=args.api_key) as analyzer:
        try:
            # Parse the requirements file
            packages = analyzer.parse_requirements_file(args.file_path)

            if not packages:
                print("No packages found in the file.")
                sys.exit(1)

            print(f"Found {len(packages)} packages to analyze.")
            print()

            # Analyze packages concurrently
            await analyzer.analyze_packages_concurrent(packages)

            print()
            print("Analysis complete! Generating report...")

            # Generate the report
            report = analyzer.generate_report(packages, args.format)

            # Handle HTML format with automatic file generation and opening
            if args.format == "html":
                # Create reports directory if it doesn't exist
                reports_dir = Path("reports")
                reports_dir.mkdir(exist_ok=True)

                if args.output:
                    # If user specified output, check if it's in reports/ already
                    if not args.output.startswith("reports/"):
                        html_file = reports_dir / args.output
                    else:
                        html_file = Path(args.output)
                else:
                    # Generate a timestamped filename in reports directory
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    html_file = reports_dir / f"vulnerability_report_{timestamp}.html"

                # Save HTML report
                with open(html_file, "w", encoding="utf-8") as f:
                    f.write(report)
                print(f"📄 HTML report saved to: {html_file}")

                # Open in browser
                try:
                    file_path = os.path.abspath(html_file)
                    webbrowser.open(f"file://{file_path}")
                    print(f"🌐 Opening report in your default browser...")
                except Exception as e:
                    print(f"⚠️  Could not open browser automatically: {e}")
                    print(f"You can manually open: {os.path.abspath(html_file)}")
            else:
                # Handle text format
                if args.output:
                    with open(args.output, "w", encoding="utf-8") as f:
                        f.write(report)
                    print(f"Report saved to: {args.output}")
                else:
                    print()
                    print(report)

        except KeyboardInterrupt:
            print("\nAnalysis interrupted by user.")
            sys.exit(1)
        except Exception as e:
            print(f"An error occurred: {e}")
            sys.exit(1)


def main():
    """Main function to run the vulnerability analyzer."""
    parser = argparse.ArgumentParser(
        description="Analyze dependency files for vulnerabilities and suggest updates",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python vulnerability_analyzer.py requirements.txt
  python vulnerability_analyzer.py requirements.txt --format html
  python vulnerability_analyzer.py requirements.txt --format html --output my_report.html
  VULNERABILITY_API_KEY=your_key python vulnerability_analyzer.py requirements.txt

Note: HTML reports are automatically saved to the 'reports/' directory
        """,
    )

    parser.add_argument(
        "file_path", help="Path to the dependency file (e.g., requirements.txt)"
    )

    parser.add_argument(
        "--format",
        choices=["text", "html"],
        default="text",
        help="Output format: 'text' for console output, 'html' to generate and open an interactive web report (default: text)",
    )

    parser.add_argument(
        "--output",
        help="Output file path. For HTML reports, files are saved to 'reports/' directory (default: auto-generated filename for HTML, stdout for text)",
    )

    parser.add_argument(
        "--api-key",
        help="API key for vulnerability database (can also use VULNERABILITY_API_KEY env var)",
    )

    args = parser.parse_args()

    # Run the async analyzer
    asyncio.run(analyze_dependencies(args))


if __name__ == "__main__":
    main()
