version: '3.8'

services:
  vulnera-rust:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - VULNERA__SERVER__HOST=0.0.0.0
      - VULNERA__SERVER__PORT=3000
      - VULNERA__LOGGING__LEVEL=info
      - RUST_LOG=info
    volumes:
      - cache_data:/app/.vulnera_cache
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  cache_data:
