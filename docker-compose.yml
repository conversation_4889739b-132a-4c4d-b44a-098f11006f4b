version: '3.8'

services:
  vulnera-rust:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - VULNERA__SERVER__HOST=0.0.0.0
      - VULNERA__SERVER__PORT=3000
      - VULNERA__LOGGING__LEVEL=info
      - RUST_LOG=info
      - VULNERA__REDIS__URL=redis://redis:6379
      - VULNERA__REDIS__POOL_SIZE=10
    volumes:
      - cache_data:/app/.vulnera_cache
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7.4-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
      start_period: 10s

  redis-sentinel:
    image: redis:7.4-alpine
    ports:
      - "26379:26379"
    volumes:
      - ./config/sentinel.conf:/usr/local/etc/redis/sentinel.conf
    command: redis-sentinel /usr/local/etc/redis/sentinel.conf
    depends_on:
      - redis
    restart: unless-stopped

volumes:
  cache_data:
  redis_data:
