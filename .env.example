# Environment variables for Vulnera Rust
# Copy this file to .env and fill in your values

# Server configuration
VULNERA__SERVER__HOST=0.0.0.0
VULNERA__SERVER__PORT=3000

# Cache configuration
VULNERA__CACHE__DIRECTORY=.vulnera_cache
VULNERA__CACHE__TTL_HOURS=24

# API Keys (optional but recommended for better rate limits)
VULNERA__APIS__NVD__API_KEY=your_nvd_api_key_here
VULNERA__APIS__GHSA__TOKEN=your_github_token_here

# Logging configuration
VULNERA__LOGGING__LEVEL=info
VULNERA__LOGGING__FORMAT=json

# Environment (development, staging, production)
ENV=development