//! Redis connection pool implementation with high availability support

use crate::config::{RedisConfig, RedisClusterConfig, RedisSentinelConfig};
use crate::application::ApplicationError;
use async_trait::async_trait;
use deadpool_redis::{Config as PoolConfig, Pool, Runtime};
use redis::{Client, Connection, RedisResult, aio::ConnectionManager};
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};

/// Redis connection pool manager with failover support
#[derive(Debug)]
pub struct RedisConnectionPool {
    pool: Arc<RwLock<Option<Pool>>>,
    config: RedisConfig,
    health_checker: Arc<RedisHealthChecker>,
}

/// Redis health checker for monitoring connection status
#[derive(Debug)]
pub struct RedisHealthChecker {
    config: RedisConfig,
    last_health_check: Arc<RwLock<Option<std::time::Instant>>>,
    is_healthy: Arc<RwLock<bool>>,
}

/// Connection pool trait for abstraction
#[async_trait]
pub trait ConnectionPool: Send + Sync {
    type Connection;
    
    async fn get_connection(&self) -> Result<Self::Connection, ApplicationError>;
    async fn is_healthy(&self) -> bool;
    async fn reconnect(&self) -> Result<(), ApplicationError>;
}

impl RedisConnectionPool {
    /// Create a new Redis connection pool
    pub async fn new(config: RedisConfig) -> Result<Self, ApplicationError> {
        let health_checker = Arc::new(RedisHealthChecker::new(config.clone()));
        let pool = Arc::new(RwLock::new(None));
        
        let connection_pool = Self {
            pool: pool.clone(),
            config: config.clone(),
            health_checker: health_checker.clone(),
        };
        
        // Initialize the connection pool
        connection_pool.initialize_pool().await?;
        
        // Start health monitoring
        let pool_clone = connection_pool.clone();
        tokio::spawn(async move {
            pool_clone.health_monitor().await;
        });
        
        Ok(connection_pool)
    }
    
    /// Initialize the Redis connection pool based on configuration
    async fn initialize_pool(&self) -> Result<(), ApplicationError> {
        let pool = if let Some(cluster_config) = &self.config.cluster {
            if cluster_config.enabled {
                self.create_cluster_pool(cluster_config).await?
            } else {
                self.create_single_pool().await?
            }
        } else if let Some(sentinel_config) = &self.config.sentinel {
            if sentinel_config.enabled {
                self.create_sentinel_pool(sentinel_config).await?
            } else {
                self.create_single_pool().await?
            }
        } else {
            self.create_single_pool().await?
        };
        
        let mut pool_guard = self.pool.write().await;
        *pool_guard = Some(pool);
        
        info!("Redis connection pool initialized successfully");
        Ok(())
    }
    
    /// Create a single Redis connection pool
    async fn create_single_pool(&self) -> Result<Pool, ApplicationError> {
        let mut pool_config = PoolConfig::from_url(&self.config.url);
        pool_config.max_size = self.config.pool_size as usize;
        
        let pool = pool_config
            .create_pool(Some(Runtime::Tokio1))
            .map_err(|e| {
                error!("Failed to create Redis pool: {}", e);
                ApplicationError::Redis(format!("Pool creation failed: {}", e))
            })?;
            
        // Test the connection
        let conn = pool.get().await.map_err(|e| {
            error!("Failed to get connection from pool: {}", e);
            ApplicationError::Redis(format!("Connection test failed: {}", e))
        })?;
        
        debug!("Single Redis pool created successfully");
        Ok(pool)
    }
    
    /// Create a Redis cluster connection pool
    async fn create_cluster_pool(&self, _cluster_config: &RedisClusterConfig) -> Result<Pool, ApplicationError> {
        // For now, fallback to single pool
        // In production, implement proper cluster support
        warn!("Redis cluster mode not fully implemented, falling back to single node");
        self.create_single_pool().await
    }
    
    /// Create a Redis sentinel connection pool
    async fn create_sentinel_pool(&self, _sentinel_config: &RedisSentinelConfig) -> Result<Pool, ApplicationError> {
        // For now, fallback to single pool
        // In production, implement proper sentinel support
        warn!("Redis sentinel mode not fully implemented, falling back to single node");
        self.create_single_pool().await
    }
    
    /// Health monitoring background task
    async fn health_monitor(&self) {
        let mut interval = tokio::time::interval(Duration::from_secs(30));
        
        loop {
            interval.tick().await;
            
            let is_healthy = self.check_health().await;
            let mut health_guard = self.health_checker.is_healthy.write().await;
            *health_guard = is_healthy;
            
            if !is_healthy {
                warn!("Redis connection unhealthy, attempting reconnection");
                if let Err(e) = self.reconnect().await {
                    error!("Failed to reconnect to Redis: {}", e);
                }
            }
        }
    }
    
    /// Check Redis connection health
    async fn check_health(&self) -> bool {
        match self.get_connection().await {
            Ok(mut conn) => {
                match redis::cmd("PING").query_async::<_, String>(&mut conn).await {
                    Ok(response) => {
                        debug!("Redis health check successful: {}", response);
                        response == "PONG"
                    }
                    Err(e) => {
                        warn!("Redis PING failed: {}", e);
                        false
                    }
                }
            }
            Err(e) => {
                warn!("Failed to get Redis connection for health check: {}", e);
                false
            }
        }
    }
}

#[async_trait]
impl ConnectionPool for RedisConnectionPool {
    type Connection = deadpool_redis::Connection;
    
    async fn get_connection(&self) -> Result<Self::Connection, ApplicationError> {
        let pool_guard = self.pool.read().await;
        
        if let Some(pool) = pool_guard.as_ref() {
            let conn = pool.get().await.map_err(|e| {
                error!("Failed to get connection from Redis pool: {}", e);
                ApplicationError::Redis(format!("Connection acquisition failed: {}", e))
            })?;
            
            Ok(conn)
        } else {
            error!("Redis pool not initialized");
            Err(ApplicationError::Redis("Pool not initialized".to_string()))
        }
    }
    
    async fn is_healthy(&self) -> bool {
        let health_guard = self.health_checker.is_healthy.read().await;
        *health_guard
    }
    
    async fn reconnect(&self) -> Result<(), ApplicationError> {
        info!("Attempting to reconnect Redis pool");
        
        // Clear the existing pool
        {
            let mut pool_guard = self.pool.write().await;
            *pool_guard = None;
        }
        
        // Reinitialize the pool
        self.initialize_pool().await?;
        
        info!("Redis pool reconnected successfully");
        Ok(())
    }
}

impl Clone for RedisConnectionPool {
    fn clone(&self) -> Self {
        Self {
            pool: self.pool.clone(),
            config: self.config.clone(),
            health_checker: self.health_checker.clone(),
        }
    }
}

impl RedisHealthChecker {
    fn new(config: RedisConfig) -> Self {
        Self {
            config,
            last_health_check: Arc::new(RwLock::new(None)),
            is_healthy: Arc::new(RwLock::new(true)),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::{CompressionConfig, CompressionAlgorithm};
    
    fn create_test_config() -> RedisConfig {
        RedisConfig {
            url: "redis://localhost:6379".to_string(),
            pool_size: 5,
            connection_timeout_ms: 1000,
            command_timeout_ms: 1000,
            retry_attempts: 2,
            retry_delay_ms: 50,
            cluster: None,
            sentinel: None,
            compression: CompressionConfig {
                enabled: false,
                algorithm: CompressionAlgorithm::None,
                min_size_bytes: 1024,
            },
            key_prefix: "test:".to_string(),
        }
    }
    
    #[tokio::test]
    async fn test_pool_creation() {
        let config = create_test_config();
        
        // This test will only pass if Redis is running
        // In CI/CD, we would use a Redis container
        if let Ok(_pool) = RedisConnectionPool::new(config).await {
            // Pool created successfully
            assert!(true);
        } else {
            // Redis not available, skip test
            println!("Redis not available, skipping test");
        }
    }
}
