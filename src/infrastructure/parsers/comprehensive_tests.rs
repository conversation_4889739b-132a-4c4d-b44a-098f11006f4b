//! Comprehensive tests for all package file parsers

use super::*;
use crate::domain::{Ecosystem, Version};

// Test data for different ecosystems

const PACKAGE_JSON_CONTENT: &str = r#"
{
    "name": "test-package",
    "version": "1.0.0",
    "dependencies": {
        "express": "^4.17.1",
        "lodash": "~4.17.21",
        "axios": "0.21.1"
    },
    "devDependencies": {
        "jest": ">=26.0.0",
        "eslint": "^7.0.0"
    },
    "peerDependencies": {
        "react": ">=16.0.0"
    }
}
"#;

const PACKAGE_LOCK_JSON_CONTENT: &str = r#"
{
    "name": "test-package",
    "version": "1.0.0",
    "lockfileVersion": 1,
    "dependencies": {
        "express": {
            "version": "4.17.1",
            "resolved": "https://registry.npmjs.org/express/-/express-4.17.1.tgz",
            "integrity": "sha512-mHJ9O79RqluphRrcw2X/GTh3k9tVv8YcoyY4Kkh4WDMUYKRZUq0h1o0w2rrrxBqM7VoeUVqgb27xlEMXTnYt4g=="
        },
        "lodash": {
            "version": "4.17.21",
            "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz",
            "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="
        }
    }
}
"#;

const YARN_LOCK_CONTENT: &str = r#"
# yarn lockfile v1

express@^4.17.1:
  version "4.17.1"
  resolved "https://registry.yarnpkg.com/express/-/express-4.17.1.tgz#4491fc38605cf51f8629d39c2b5d026f98a4c134"
  integrity sha512-mHJ9O79RqluphRrcw2X/GTh3k9tVv8YcoyY4Kkh4WDMUYKRZUq0h1o0w2rrrxBqM7VoeUVqgb27xlEMXTnYt4g==

lodash@~4.17.21:
  version "4.17.21"
  resolved "https://registry.yarnpkg.com/lodash/-/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==
"#;

const REQUIREMENTS_TXT_CONTENT: &str = r#"
# Production dependencies
Django==3.2.5
requests>=2.25.1,<3.0.0
numpy~=1.21.0
pandas>=1.3.0
# Development dependencies
pytest==6.2.4
black>=21.0.0
"#;

const PIPFILE_CONTENT: &str = r#"
[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
django = "==3.2.5"
requests = ">=2.25.1,<3.0.0"
numpy = "~=1.21.0"

[dev-packages]
pytest = "==6.2.4"
black = ">=21.0.0"

[requires]
python_version = "3.9"
"#;

const PYPROJECT_TOML_CONTENT: &str = r#"
[build-system]
requires = ["setuptools", "wheel"]

[project]
name = "test-package"
version = "1.0.0"
dependencies = [
    "django==3.2.5",
    "requests>=2.25.1,<3.0.0",
    "numpy~=1.21.0"
]

[project.optional-dependencies]
dev = [
    "pytest==6.2.4",
    "black>=21.0.0"
]
"#;

const POM_XML_CONTENT: &str = r#"
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/x.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.example</groupId>
    <artifactId>test-project</artifactId>
    <version>1.0.0</version>

    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>5.3.8</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.12.3</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
"#;

const BUILD_GRADLE_CONTENT: &str = r#"
plugins {
    id 'java'
    id 'org.springframework.boot' version '2.5.2'
}

dependencies {
    implementation 'org.springframework:spring-core:5.3.8'
    implementation 'com.fasterxml.jackson.core:jackson-core:2.12.3'
    testImplementation 'junit:junit:4.13.2'
    runtimeOnly 'mysql:mysql-connector-java:8.0.25'
}

repositories {
    mavenCentral()
}
"#;

const CARGO_TOML_CONTENT: &str = r#"
[package]
name = "test-package"
version = "0.1.0"
edition = "2021"

[dependencies]
serde = { version = "1.0", features = ["derive"] }
tokio = { version = "1.0", features = ["full"] }
reqwest = "0.11"

[dev-dependencies]
tokio-test = "0.4"

[build-dependencies]
cc = "1.0"
"#;

const CARGO_LOCK_CONTENT: &str = r#"
# This file is automatically @generated by Cargo.
# It is not intended for manual editing.
version = 3

[[package]]
name = "serde"
version = "1.0.136"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce31e24b01e1e524df96f1c2fdd054405f8d7376249a5110886fb4b658484789"

[[package]]
name = "tokio"
version = "1.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2af73ac49756f3f7c01172e34a23e5d0216f6c32333757c2c61feb2bbff30a07"

[[package]]
name = "test-package"
version = "0.1.0"
dependencies = [
 "serde",
 "tokio",
]
"#;

const GO_MOD_CONTENT: &str = r#"
module example.com/test-package

go 1.18

require (
    github.com/gin-gonic/gin v1.7.2
    github.com/stretchr/testify v1.7.0
    golang.org/x/crypto v0.0.0-20210616213533-5ff15b29337e
)

require (
    github.com/davecgh/go-spew v1.1.1 // indirect
    github.com/pmezard/go-difflib v1.0.0 // indirect
    gopkg.in/yaml.v3 v3.0.0-20210107192922-496545a6307b // indirect
)
"#;

const COMPOSER_JSON_CONTENT: &str = r#"
{
    "name": "example/test-package",
    "description": "A test package",
    "type": "library",
    "require": {
        "php": ">=7.4",
        "symfony/console": "^5.3",
        "guzzlehttp/guzzle": "~7.0",
        "monolog/monolog": ">=2.0,<3.0"
    },
    "require-dev": {
        "phpunit/phpunit": "^9.5",
        "squizlabs/php_codesniffer": "^3.6"
    },
    "autoload": {
        "psr-4": {
            "Example\\": "src/"
        }
    }
}
"#;

// Comprehensive parser tests

#[tokio::test]
async fn test_npm_parser_comprehensive() {
    let parser = npm::NpmParser::new();

    assert!(parser.supports_file("package.json"));
    assert!(!parser.supports_file("requirements.txt"));
    assert_eq!(parser.ecosystem(), Ecosystem::Npm);

    let packages = parser.parse_file(PACKAGE_JSON_CONTENT).await.unwrap();

    // Should parse all dependencies (dependencies + devDependencies + peerDependencies)
    assert!(packages.len() >= 5);

    // Check specific packages
    let express = packages.iter().find(|p| p.name == "express").unwrap();
    assert_eq!(express.version, Version::parse("4.17.1").unwrap());
    assert_eq!(express.ecosystem, Ecosystem::Npm);

    let lodash = packages.iter().find(|p| p.name == "lodash").unwrap();
    assert_eq!(lodash.version, Version::parse("4.17.21").unwrap());
}

#[tokio::test]
async fn test_package_lock_parser_comprehensive() {
    let parser = npm::PackageLockParser::new();

    assert!(parser.supports_file("package-lock.json"));
    assert_eq!(parser.ecosystem(), Ecosystem::Npm);

    let packages = parser.parse_file(PACKAGE_LOCK_JSON_CONTENT).await.unwrap();
    assert_eq!(packages.len(), 2);

    let express = packages.iter().find(|p| p.name == "express").unwrap();
    assert_eq!(express.version, Version::parse("4.17.1").unwrap());
}

#[tokio::test]
async fn test_yarn_lock_parser_comprehensive() {
    let parser = npm::YarnLockParser::new();

    assert!(parser.supports_file("yarn.lock"));
    assert_eq!(parser.ecosystem(), Ecosystem::Npm);

    let packages = parser.parse_file(YARN_LOCK_CONTENT).await.unwrap();
    assert_eq!(packages.len(), 2);

    let express = packages.iter().find(|p| p.name == "express").unwrap();
    assert_eq!(express.version, Version::parse("4.17.1").unwrap());
}

#[tokio::test]
async fn test_requirements_txt_parser_comprehensive() {
    let parser = python::RequirementsTxtParser::new();

    assert!(parser.supports_file("requirements.txt"));
    assert_eq!(parser.ecosystem(), Ecosystem::PyPI);

    let packages = parser.parse_file(REQUIREMENTS_TXT_CONTENT).await.unwrap();
    assert!(packages.len() >= 5);

    let django = packages.iter().find(|p| p.name == "Django").unwrap();
    assert_eq!(django.version, Version::parse("3.2.5").unwrap());
    assert_eq!(django.ecosystem, Ecosystem::PyPI);
}

#[tokio::test]
async fn test_pipfile_parser_comprehensive() {
    let parser = python::PipfileParser::new();

    assert!(parser.supports_file("Pipfile"));
    assert_eq!(parser.ecosystem(), Ecosystem::PyPI);

    let packages = parser.parse_file(PIPFILE_CONTENT).await.unwrap();
    assert!(packages.len() >= 3);

    let django = packages.iter().find(|p| p.name == "django").unwrap();
    assert_eq!(django.version, Version::parse("3.2.5").unwrap());
}

#[tokio::test]
async fn test_pyproject_toml_parser_comprehensive() {
    let parser = python::PyProjectTomlParser::new();

    assert!(parser.supports_file("pyproject.toml"));
    assert_eq!(parser.ecosystem(), Ecosystem::PyPI);

    let packages = parser.parse_file(PYPROJECT_TOML_CONTENT).await.unwrap();
    assert!(packages.len() >= 3);

    let django = packages.iter().find(|p| p.name == "django").unwrap();
    assert_eq!(django.version, Version::parse("3.2.5").unwrap());
}

#[tokio::test]
async fn test_maven_parser_comprehensive() {
    let parser = java::MavenParser::new();

    assert!(parser.supports_file("pom.xml"));
    assert_eq!(parser.ecosystem(), Ecosystem::Maven);

    let packages = parser.parse_file(POM_XML_CONTENT).await.unwrap();
    assert_eq!(packages.len(), 3);

    let spring = packages
        .iter()
        .find(|p| p.name == "org.springframework:spring-core")
        .unwrap();
    assert_eq!(spring.version, Version::parse("5.3.8").unwrap());
    assert_eq!(spring.ecosystem, Ecosystem::Maven);
}

#[tokio::test]
async fn test_gradle_parser_comprehensive() {
    let parser = java::GradleParser::new();

    assert!(parser.supports_file("build.gradle"));
    assert_eq!(parser.ecosystem(), Ecosystem::Maven);

    let packages = parser.parse_file(BUILD_GRADLE_CONTENT).await.unwrap();
    assert!(packages.len() >= 3);

    let spring = packages
        .iter()
        .find(|p| p.name == "org.springframework:spring-core")
        .unwrap();
    assert_eq!(spring.version, Version::parse("5.3.8").unwrap());
}

#[tokio::test]
async fn test_cargo_toml_parser_comprehensive() {
    let parser = rust::CargoParser::new();

    assert!(parser.supports_file("Cargo.toml"));
    assert_eq!(parser.ecosystem(), Ecosystem::Cargo);

    let packages = parser.parse_file(CARGO_TOML_CONTENT).await.unwrap();
    assert!(packages.len() >= 3);

    let serde = packages.iter().find(|p| p.name == "serde").unwrap();
    assert_eq!(serde.version, Version::parse("1.0.0").unwrap());
    assert_eq!(serde.ecosystem, Ecosystem::Cargo);
}

#[tokio::test]
async fn test_cargo_lock_parser_comprehensive() {
    let parser = rust::CargoLockParser::new();

    assert!(parser.supports_file("Cargo.lock"));
    assert_eq!(parser.ecosystem(), Ecosystem::Cargo);

    let packages = parser.parse_file(CARGO_LOCK_CONTENT).await.unwrap();
    assert_eq!(packages.len(), 3); // serde, tokio, and test-package

    let serde = packages.iter().find(|p| p.name == "serde").unwrap();
    assert_eq!(serde.version, Version::parse("1.0.136").unwrap());
}

#[tokio::test]
async fn test_go_mod_parser_comprehensive() {
    let parser = go::GoModParser::new();

    assert!(parser.supports_file("go.mod"));
    assert_eq!(parser.ecosystem(), Ecosystem::Go);

    let packages = parser.parse_file(GO_MOD_CONTENT).await.unwrap();
    assert!(packages.len() >= 3);

    let gin = packages
        .iter()
        .find(|p| p.name == "github.com/gin-gonic/gin")
        .unwrap();
    assert_eq!(gin.version, Version::parse("1.7.2").unwrap());
    assert_eq!(gin.ecosystem, Ecosystem::Go);
}

#[tokio::test]
async fn test_composer_json_parser_comprehensive() {
    let parser = php::ComposerParser::new();

    assert!(parser.supports_file("composer.json"));
    assert_eq!(parser.ecosystem(), Ecosystem::Packagist);

    let packages = parser.parse_file(COMPOSER_JSON_CONTENT).await.unwrap();
    assert!(packages.len() >= 4);

    let symfony = packages
        .iter()
        .find(|p| p.name == "symfony/console")
        .unwrap();
    assert_eq!(symfony.version, Version::parse("5.3.0").unwrap());
    assert_eq!(symfony.ecosystem, Ecosystem::Packagist);
}

#[tokio::test]
async fn test_parser_factory_comprehensive() {
    let factory = ParserFactory::new();

    // Test all supported file types
    assert!(factory.create_parser("package.json").is_some());
    assert!(factory.create_parser("package-lock.json").is_some());
    assert!(factory.create_parser("yarn.lock").is_some());
    assert!(factory.create_parser("requirements.txt").is_some());
    assert!(factory.create_parser("Pipfile").is_some());
    assert!(factory.create_parser("pyproject.toml").is_some());
    assert!(factory.create_parser("pom.xml").is_some());
    assert!(factory.create_parser("build.gradle").is_some());
    assert!(factory.create_parser("Cargo.toml").is_some());
    assert!(factory.create_parser("Cargo.lock").is_some());
    assert!(factory.create_parser("go.mod").is_some());
    assert!(factory.create_parser("composer.json").is_some());

    // Test unsupported file types
    assert!(factory.create_parser("unknown.txt").is_none());
    assert!(factory.create_parser("README.md").is_none());
}

// Error handling tests

#[tokio::test]
async fn test_parser_error_handling_invalid_json() {
    let parser = npm::NpmParser::new();
    let invalid_json = r#"{"invalid": json"#;

    let result = parser.parse_file(invalid_json).await;
    assert!(result.is_err());
}

#[tokio::test]
async fn test_parser_error_handling_missing_dependencies() {
    let parser = npm::NpmParser::new();
    let no_deps = r#"{"name": "test", "version": "1.0.0"}"#;

    let packages = parser.parse_file(no_deps).await.unwrap();
    assert_eq!(packages.len(), 0);
}

#[tokio::test]
async fn test_parser_error_handling_invalid_version() {
    let parser = npm::NpmParser::new();
    let invalid_version = r#"
    {
        "dependencies": {
            "express": "invalid-version"
        }
    }
    "#;

    // Should handle invalid versions gracefully
    let result = parser.parse_file(invalid_version).await;
    // Depending on implementation, this might succeed with empty results or fail
    // The important thing is it doesn't panic
    let _ = result;
}

#[tokio::test]
async fn test_parser_error_handling_empty_content() {
    let parser = npm::NpmParser::new();
    let result = parser.parse_file("").await;
    assert!(result.is_err());
}

#[tokio::test]
async fn test_parser_error_handling_malformed_xml() {
    let parser = java::MavenParser::new();
    let malformed_xml = r#"<project><dependencies><dependency>missing closing tags"#;

    let result = parser.parse_file(malformed_xml).await;
    // The regex-based parser is lenient and returns empty result for malformed XML
    assert!(result.is_ok());
    assert!(result.unwrap().is_empty());
}

#[tokio::test]
async fn test_parser_error_handling_malformed_toml() {
    let parser = rust::CargoParser::new();
    let malformed_toml = r#"
    [dependencies
    serde = "1.0"
    "#;

    let result = parser.parse_file(malformed_toml).await;
    assert!(result.is_err());
}

// Edge case tests

#[tokio::test]
async fn test_parser_edge_cases_empty_dependencies() {
    let parser = npm::NpmParser::new();
    let empty_deps = r#"
    {
        "name": "test",
        "version": "1.0.0",
        "dependencies": {},
        "devDependencies": {}
    }
    "#;

    let packages = parser.parse_file(empty_deps).await.unwrap();
    assert_eq!(packages.len(), 0);
}

#[tokio::test]
async fn test_parser_edge_cases_version_ranges() {
    let parser = npm::NpmParser::new();
    let version_ranges = r#"
    {
        "dependencies": {
            "express": "^4.17.1",
            "lodash": "~4.17.21",
            "axios": ">=0.21.0 <1.0.0",
            "moment": "*",
            "debug": "latest"
        }
    }
    "#;

    let packages = parser.parse_file(version_ranges).await.unwrap();
    assert!(packages.len() >= 3); // Should handle most version formats
}

#[tokio::test]
async fn test_parser_edge_cases_large_file() {
    let parser = npm::NpmParser::new();

    // Create a large package.json with many dependencies
    let mut large_deps = String::from(r#"{"dependencies": {"#);
    for i in 0..100 {
        if i > 0 {
            large_deps.push(',');
        }
        large_deps.push_str(&format!(r#""package-{i}": "1.0.{i}""#));
    }
    large_deps.push_str("}}");

    let packages = parser.parse_file(&large_deps).await.unwrap();
    assert_eq!(packages.len(), 100);
}

#[tokio::test]
async fn test_parser_edge_cases_unicode_names() {
    let parser = npm::NpmParser::new();
    let unicode_names = r#"
    {
        "dependencies": {
            "测试包": "1.0.0",
            "пакет": "2.0.0",
            "パッケージ": "3.0.0"
        }
    }
    "#;

    let packages = parser.parse_file(unicode_names).await.unwrap();
    assert_eq!(packages.len(), 3);
}

// Performance tests

#[tokio::test]
async fn test_parser_performance_concurrent_parsing() {
    use std::time::Instant;
    use tokio::task::JoinSet;

    let _parser = npm::NpmParser::new();
    let mut join_set = JoinSet::new();

    let start = Instant::now();

    // Parse multiple files concurrently
    for _ in 0..10 {
        let parser_clone = npm::NpmParser::new();
        let content = PACKAGE_JSON_CONTENT.to_string();
        join_set.spawn(async move { parser_clone.parse_file(&content).await });
    }

    let mut results = Vec::new();
    while let Some(result) = join_set.join_next().await {
        results.push(result.unwrap());
    }

    let duration = start.elapsed();

    // All should succeed
    assert_eq!(results.len(), 10);
    for result in results {
        assert!(result.is_ok());
    }

    // Should complete reasonably quickly (adjust threshold as needed)
    assert!(duration.as_secs() < 5);
}

#[tokio::test]
async fn test_parser_memory_usage() {
    let parser = npm::NpmParser::new();

    // Parse the same content multiple times to check for memory leaks
    for _ in 0..100 {
        let packages = parser.parse_file(PACKAGE_JSON_CONTENT).await.unwrap();
        assert!(!packages.is_empty());
    }

    // If we get here without running out of memory, the test passes
}
