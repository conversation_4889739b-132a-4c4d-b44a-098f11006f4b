# Vulnerability APIs Research

## Overview
This document covers the three main vulnerability data sources we'll integrate: OSV API, NVD API, and GitHub Security Advisories (GHSA).

## OSV API (Open Source Vulnerability Database)

### Endpoint Information
- Base URL: `https://api.osv.dev/v1`
- Rate Limits: No explicit rate limits, but recommended 0.1s delay between requests
- Authentication: None required
- Format: JSON

### Key Endpoints

#### Query Vulnerabilities
```
POST /query
```

Request Body:
```json
{
  "package": {
    "name": "package-name",
    "ecosystem": "npm"
  },
  "version": "1.0.0"
}
```

#### Get Vulnerability by ID
```
GET /vulns/{id}
```

### Response Structure
```rust
#[derive(Deserialize, Debug)]
struct OsvResponse {
    vulns: Vec<OsvVulnerability>,
}

#[derive(Deserialize, Debug)]
struct OsvVulnerability {
    id: String,
    summary: String,
    details: String,
    aliases: Vec<String>,
    modified: String,
    published: String,
    affected: Vec<OsvAffected>,
    severity: Option<Vec<OsvSeverity>>,
    references: Vec<OsvReference>,
}

#[derive(Deserialize, Debug)]
struct OsvAffected {
    package: OsvPackage,
    ranges: Vec<OsvRange>,
    versions: Option<Vec<String>>,
    ecosystem_specific: Option<serde_json::Value>,
}

#[derive(Deserialize, Debug)]
struct OsvPackage {
    name: String,
    ecosystem: String,
    purl: Option<String>,
}

#[derive(Deserialize, Debug)]
struct OsvRange {
    #[serde(rename = "type")]
    range_type: String,
    events: Vec<OsvEvent>,
}

#[derive(Deserialize, Debug)]
struct OsvEvent {
    introduced: Option<String>,
    fixed: Option<String>,
    last_affected: Option<String>,
}

#[derive(Deserialize, Debug)]
struct OsvSeverity {
    #[serde(rename = "type")]
    severity_type: String,
    score: String,
}
```

### Supported Ecosystems
- npm (Node.js)
- PyPI (Python)
- RubyGems (Ruby)
- crates.io (Rust)
- Maven (Java)
- Go
- Packagist (PHP)
- NuGet (.NET)

## NVD API (National Vulnerability Database)

### Endpoint Information
- Base URL: `https://services.nvd.nist.gov/rest/json`
- Rate Limits: 5 requests per 30 seconds (without API key), 50 requests per 30 seconds (with API key)
- Authentication: Optional API key for higher rate limits
- Format: JSON

### Key Endpoints

#### Search CVEs
```
GET /cves/2.0?keywordSearch={package_name}
```

#### Get CVE by ID
```
GET /cves/2.0?cveId={cve_id}
```

### Response Structure
```rust
#[derive(Deserialize, Debug)]
struct NvdResponse {
    #[serde(rename = "resultsPerPage")]
    results_per_page: u32,
    #[serde(rename = "startIndex")]
    start_index: u32,
    #[serde(rename = "totalResults")]
    total_results: u32,
    format: String,
    version: String,
    timestamp: String,
    vulnerabilities: Vec<NvdVulnerability>,
}

#[derive(Deserialize, Debug)]
struct NvdVulnerability {
    cve: NvdCve,
}

#[derive(Deserialize, Debug)]
struct NvdCve {
    id: String,
    #[serde(rename = "sourceIdentifier")]
    source_identifier: String,
    published: String,
    #[serde(rename = "lastModified")]
    last_modified: String,
    #[serde(rename = "vulnStatus")]
    vuln_status: String,
    descriptions: Vec<NvdDescription>,
    metrics: Option<NvdMetrics>,
    weaknesses: Option<Vec<NvdWeakness>>,
    configurations: Option<Vec<NvdConfiguration>>,
    references: Vec<NvdReference>,
}

#[derive(Deserialize, Debug)]
struct NvdMetrics {
    #[serde(rename = "cvssMetricV31")]
    cvss_metric_v31: Option<Vec<NvdCvssV31>>,
    #[serde(rename = "cvssMetricV30")]
    cvss_metric_v30: Option<Vec<NvdCvssV30>>,
    #[serde(rename = "cvssMetricV2")]
    cvss_metric_v2: Option<Vec<NvdCvssV2>>,
}

#[derive(Deserialize, Debug)]
struct NvdCvssV31 {
    source: String,
    #[serde(rename = "type")]
    cvss_type: String,
    #[serde(rename = "cvssData")]
    cvss_data: NvdCvssData,
    #[serde(rename = "exploitabilityScore")]
    exploitability_score: f64,
    #[serde(rename = "impactScore")]
    impact_score: f64,
}
```

### Rate Limiting Strategy
```rust
use std::time::{Duration, Instant};
use tokio::time::sleep;

struct NvdClient {
    last_request: Option<Instant>,
    api_key: Option<String>,
}

impl NvdClient {
    async fn make_request(&mut self, url: &str) -> Result<reqwest::Response, reqwest::Error> {
        // Enforce rate limiting
        if let Some(last) = self.last_request {
            let elapsed = last.elapsed();
            let min_interval = if self.api_key.is_some() {
                Duration::from_millis(600) // 50 requests per 30 seconds
            } else {
                Duration::from_secs(6) // 5 requests per 30 seconds
            };
            
            if elapsed < min_interval {
                sleep(min_interval - elapsed).await;
            }
        }
        
        let client = reqwest::Client::new();
        let mut request = client.get(url);
        
        if let Some(ref api_key) = self.api_key {
            request = request.header("apiKey", api_key);
        }
        
        self.last_request = Some(Instant::now());
        request.send().await
    }
}
```

## GitHub Security Advisories (GHSA)

### Endpoint Information
- Base URL: `https://api.github.com/graphql`
- Rate Limits: 5000 requests per hour (authenticated)
- Authentication: GitHub Personal Access Token required "user has to add his token if they want to use it"
- Format: GraphQL

### GraphQL Query Structure
```graphql
query($first: Int!, $after: String) {
  securityAdvisories(first: $first, after: $after) {
    pageInfo {
      hasNextPage
      endCursor
    }
    nodes {
      ghsaId
      summary
      description
      severity
      publishedAt
      updatedAt
      withdrawnAt
      vulnerabilities(first: 100) {
        nodes {
          package {
            name
            ecosystem
          }
          vulnerableVersionRange
          firstPatchedVersion {
            identifier
          }
        }
      }
      references {
        url
      }
      cwes(first: 10) {
        nodes {
          cweId
          name
        }
      }
    }
  }
}
```

### Response Structure
```rust
#[derive(Deserialize, Debug)]
struct GhsaResponse {
    data: GhsaData,
}

#[derive(Deserialize, Debug)]
struct GhsaData {
    #[serde(rename = "securityAdvisories")]
    security_advisories: GhsaSecurityAdvisories,
}

#[derive(Deserialize, Debug)]
struct GhsaSecurityAdvisories {
    #[serde(rename = "pageInfo")]
    page_info: GhsaPageInfo,
    nodes: Vec<GhsaAdvisory>,
}

#[derive(Deserialize, Debug)]
struct GhsaPageInfo {
    #[serde(rename = "hasNextPage")]
    has_next_page: bool,
    #[serde(rename = "endCursor")]
    end_cursor: Option<String>,
}

#[derive(Deserialize, Debug)]
struct GhsaAdvisory {
    #[serde(rename = "ghsaId")]
    ghsa_id: String,
    summary: String,
    description: String,
    severity: String,
    #[serde(rename = "publishedAt")]
    published_at: String,
    #[serde(rename = "updatedAt")]
    updated_at: String,
    vulnerabilities: GhsaVulnerabilities,
    references: Vec<GhsaReference>,
    cwes: GhsaCwes,
}

#[derive(Deserialize, Debug)]
struct GhsaVulnerabilities {
    nodes: Vec<GhsaVulnerability>,
}

#[derive(Deserialize, Debug)]
struct GhsaVulnerability {
    package: GhsaPackage,
    #[serde(rename = "vulnerableVersionRange")]
    vulnerable_version_range: String,
    #[serde(rename = "firstPatchedVersion")]
    first_patched_version: Option<GhsaVersion>,
}

#[derive(Deserialize, Debug)]
struct GhsaPackage {
    name: String,
    ecosystem: String,
}
```

## Ecosystem Mapping

### Package File to Ecosystem Mapping
```rust
use std::path::Path;

#[derive(Debug, Clone)]
enum Ecosystem {
    Npm,
    PyPI,
    Maven,
    Cargo,
    Go,
    Packagist,
    RubyGems,
    NuGet,
}

impl Ecosystem {
    fn from_file_path(path: &Path) -> Option<Self> {
        match path.file_name()?.to_str()? {
            "package.json" | "package-lock.json" | "yarn.lock" => Some(Self::Npm),
            "requirements.txt" | "Pipfile" | "pyproject.toml" => Some(Self::PyPI),
            "pom.xml" | "build.gradle" | "build.gradle.kts" => Some(Self::Maven),
            "Cargo.toml" | "Cargo.lock" => Some(Self::Cargo),
            "go.mod" | "go.sum" => Some(Self::Go),
            "composer.json" | "composer.lock" => Some(Self::Packagist),
            "Gemfile" | "Gemfile.lock" => Some(Self::RubyGems),
            _ => None,
        }
    }
    
    fn to_osv_ecosystem(&self) -> &'static str {
        match self {
            Self::Npm => "npm",
            Self::PyPI => "PyPI",
            Self::Maven => "Maven",
            Self::Cargo => "crates.io",
            Self::Go => "Go",
            Self::Packagist => "Packagist",
            Self::RubyGems => "RubyGems",
            Self::NuGet => "NuGet",
        }
    }
}
```

## Aggregation Strategy

### Vulnerability Deduplication
```rust
use std::collections::HashMap;

#[derive(Debug, Clone)]
struct AggregatedVulnerability {
    primary_id: String,
    aliases: Vec<String>,
    sources: Vec<VulnerabilitySource>,
    severity: Option<Severity>,
    summary: String,
    description: String,
    affected_packages: Vec<String>,
    references: Vec<String>,
}

#[derive(Debug, Clone)]
enum VulnerabilitySource {
    Osv(OsvVulnerability),
    Nvd(NvdVulnerability),
    Ghsa(GhsaAdvisory),
}

fn aggregate_vulnerabilities(
    osv_vulns: Vec<OsvVulnerability>,
    nvd_vulns: Vec<NvdVulnerability>,
    ghsa_vulns: Vec<GhsaAdvisory>,
) -> Vec<AggregatedVulnerability> {
    let mut vulnerability_map: HashMap<String, AggregatedVulnerability> = HashMap::new();
    
    // Process OSV vulnerabilities first (primary source)
    for vuln in osv_vulns {
        let aggregated = AggregatedVulnerability {
            primary_id: vuln.id.clone(),
            aliases: vuln.aliases.clone(),
            sources: vec![VulnerabilitySource::Osv(vuln.clone())],
            severity: extract_severity_from_osv(&vuln),
            summary: vuln.summary.clone(),
            description: vuln.details.clone(),
            affected_packages: extract_packages_from_osv(&vuln),
            references: extract_references_from_osv(&vuln),
        };
        
        vulnerability_map.insert(vuln.id, aggregated);
    }
    
    // Merge NVD and GHSA data based on aliases/CVE IDs
    // Implementation details...
    
    vulnerability_map.into_values().collect()
}
```

## Error Handling and Resilience

### Circuit Breaker Pattern
```rust
use std::sync::atomic::{AtomicU32, Ordering};
use std::time::{Duration, Instant};

#[derive(Debug)]
enum CircuitState {
    Closed,
    Open(Instant),
    HalfOpen,
}

struct CircuitBreaker {
    failure_count: AtomicU32,
    failure_threshold: u32,
    timeout: Duration,
    state: std::sync::Mutex<CircuitState>,
}

impl CircuitBreaker {
    async fn call<F, T, E>(&self, f: F) -> Result<T, E>
    where
        F: std::future::Future<Output = Result<T, E>>,
    {
        // Circuit breaker logic implementation
        match self.get_state() {
            CircuitState::Open(opened_at) => {
                if opened_at.elapsed() > self.timeout {
                    self.set_state(CircuitState::HalfOpen);
                } else {
                    return Err(/* circuit open error */);
                }
            }
            _ => {}
        }
        
        match f.await {
            Ok(result) => {
                self.on_success();
                Ok(result)
            }
            Err(error) => {
                self.on_failure();
                Err(error)
            }
        }
    }
}
```